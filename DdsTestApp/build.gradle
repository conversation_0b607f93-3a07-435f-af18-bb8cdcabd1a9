plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.seres.dds.test'
    compileSdk 34

    defaultConfig {
        applicationId "com.seres.dds.test"
        minSdk 31
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            // enableV3Signing true
            storeFile file('../keystore/platform.jks')
            storePassword "seres@2025"
            keyAlias "seres"
            keyPassword "seres@2025"
        }
        release {
            storeFile file('../keystore/platform.jks')
            storePassword "seres@2025"
            keyAlias "seres"
            keyPassword "seres@2025"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.activity
    implementation libs.androidx.constraintlayout
    implementation libs.gson
    // DDS库依赖
    implementation files("../UDiskBackgroundUpgradeService/libs/nativelib-release.aar")
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.8.0"
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}

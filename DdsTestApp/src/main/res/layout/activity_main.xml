<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 服务状态 -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/service_status"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/darker_gray"
        android:padding="8dp"
        android:textColor="@android:color/white" />

    <!-- 控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/startServiceButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/start_service"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/stopServiceButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/stop_service"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/sendTestDataButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/send_test_data"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/clearLogButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/clear_log"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <!-- 消息显示区域 -->
    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 接收到的消息 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/received_messages"
                android:textStyle="bold"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:layout_marginBottom="16dp"
                android:background="@android:color/darker_gray"
                android:padding="2dp">

                <ScrollView
                    android:id="@+id/receivedScrollView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scrollbars="vertical"
                    android:fadeScrollbars="false">

                    <TextView
                        android:id="@+id/receivedMessagesText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:fontFamily="monospace"
                        android:gravity="top|start"
                        android:text="" />

                </ScrollView>

            </FrameLayout>

            <!-- 发送的消息 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/sent_messages"
                android:textStyle="bold"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="150dp"
                android:background="@android:color/darker_gray"
                android:padding="2dp">

                <ScrollView
                    android:id="@+id/sentScrollView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scrollbars="vertical"
                    android:fadeScrollbars="false">

                    <TextView
                        android:id="@+id/sentMessagesText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:fontFamily="monospace"
                        android:gravity="top|start"
                        android:text="" />

                </ScrollView>

            </FrameLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>

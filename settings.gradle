pluginManagement {
    repositories {
        maven { url 'https://repo.seres.cn/nexus/repository/maven/' }
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)

    repositories {
        maven { url 'https://repo.seres.cn/nexus/repository/maven/' }
        maven { url 'https://repo.seres.cn/nexus/repository/maven-JM3.0/' }
        maven { url 'https://repo.seres.cn/nexus/repository/maven-JM3.0-SNAPSHOT/' }
        google()
        mavenCentral()
    }
}

rootProject.name = "OTAAndroidUdiskService"
include ':UDiskBackgroundUpgradeService'
// include ':DdsTestApp'
// include ':nativelib'
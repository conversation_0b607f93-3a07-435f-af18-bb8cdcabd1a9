<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.USB_PERMISSION" />
    <uses-feature android:name="android.hardware.usb.host" android:required="true" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.READ_MEDIA_STORAGE" tools:ignore="ProtectedPermissions" />

    <!-- 查询其他应用包名的权限 -->
    <queries>
        <package android:name="com.seres.dds" />
        <package android:name="com.seres.dds.test" />
    </queries>

    <application
        android:icon="@android:drawable/ic_menu_manage"
        android:label="@string/app_name"
        android:theme="@android:style/Theme.NoDisplay"
        android:requestLegacyExternalStorage="true"
        android:persistent="true"
        android:killAfterRestore="false"
        android:restoreAnyVersion="true">

        <!-- USB检测服务 -->
        <service
            android:name=".service.UsbDetectionService"
            android:enabled="true"
            android:exported="true"
            android:persistent="true"
            android:foregroundServiceType="dataSync"
            android:stopWithTask="false">
            <intent-filter>
                <action android:name="com.seres.background.upgrade.START_SERVICE" />
                <action android:name="com.seres.background.upgrade.USB_DETECTION" />
            </intent-filter>
        </service>

        <!-- 开机启动接收器 -->
        <receiver
            android:name=".receiver.BootCompleteReceiver"
            android:enabled="true"
            android:exported="true"
            android:directBootAware="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- 调试Activity (隐藏) -->
        <activity
            android:name=".DebugActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light.Dialog"
            android:excludeFromRecents="true"
            android:noHistory="true">
        </activity>

    </application>

</manifest>
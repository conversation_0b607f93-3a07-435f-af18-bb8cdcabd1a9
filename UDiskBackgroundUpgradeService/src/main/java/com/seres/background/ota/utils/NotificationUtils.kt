package com.seres.background.ota.utils

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import androidx.core.app.NotificationCompat

/**
 * 通知工具类
 * 提供统一的通知管理功能，方便各个模块使用
 */
object NotificationUtils {

    // 通用通知ID定义
    const val USB_DETECTION_NOTIFICATION_ID = 2001
    const val ERROR_NOTIFICATION_ID = 2004
    const val COMPLETION_NOTIFICATION_ID = 2005

    // 通用通知渠道ID定义
    const val USB_DETECTION_CHANNEL_ID = "usb_detection_channel"
    const val ERROR_CHANNEL_ID = "error_notification_channel"
    const val GENERAL_CHANNEL_ID = "general_notification_channel"

    /**
     * 创建通知渠道
     * @param context 上下文
     * @param channelId 渠道ID
     * @param channelName 渠道名称
     * @param description 渠道描述
     * @param importance 重要性级别，默认为低重要性
     */
    fun createNotificationChannel(
        context: Context,
        channelId: String,
        channelName: String,
        description: String,
        importance: Int = NotificationManager.IMPORTANCE_LOW
    ) {
        val channel = NotificationChannel(channelId, channelName, importance).apply {
            this.description = description
            setShowBadge(false)
        }

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    /**
     * 创建基础通知
     * @param context 上下文
     * @param channelId 渠道ID
     * @param title 通知标题
     * @param content 通知内容
     * @param iconResId 图标资源ID，默认使用系统管理图标
     * @param isOngoing 是否为持续通知，默认为true
     * @param autoCancel 是否自动取消，默认为false
     * @return 创建的通知对象
     */
    fun createNotification(
        context: Context,
        channelId: String,
        title: String,
        content: String,
        iconResId: Int = android.R.drawable.ic_menu_manage,
        isOngoing: Boolean = true,
        autoCancel: Boolean = false
    ): Notification {
        return NotificationCompat.Builder(context, channelId)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(iconResId)
            .setOngoing(isOngoing)
            .setAutoCancel(autoCancel)
            .build()
    }

    /**
     * 显示通知
     * @param context 上下文
     * @param notificationId 通知ID
     * @param notification 通知对象
     */
    fun showNotification(context: Context, notificationId: Int, notification: Notification) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(notificationId, notification)
    }

    /**
     * 更新通知内容
     * @param context 上下文
     * @param notificationId 通知ID
     * @param channelId 渠道ID
     * @param title 通知标题
     * @param content 新的通知内容
     * @param iconResId 图标资源ID，默认使用系统管理图标
     * @param isOngoing 是否为持续通知，默认为true
     * @param autoCancel 是否自动取消，默认为false
     */
    fun updateNotification(
        context: Context,
        notificationId: Int,
        channelId: String,
        title: String,
        content: String,
        iconResId: Int = android.R.drawable.ic_menu_manage,
        isOngoing: Boolean = true,
        autoCancel: Boolean = false
    ) {
        val notification = createNotification(context, channelId, title, content, iconResId, isOngoing, autoCancel)
        showNotification(context, notificationId, notification)
    }

    /**
     * 取消通知
     * @param context 上下文
     * @param notificationId 通知ID
     */
    fun cancelNotification(context: Context, notificationId: Int) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(notificationId)
    }

    /**
     * 取消所有通知
     * @param context 上下文
     */
    fun cancelAllNotifications(context: Context) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
    }

    // ========== 便捷方法 - 简化常用通知操作 ==========

    /**
     * 初始化USB检测通知渠道并显示前台服务通知
     * @param context 上下文
     * @return 创建的通知对象，用于startForeground
     */
    fun initUsbDetectionNotification(context: Context): Notification {
        createNotificationChannel(
            context,
            USB_DETECTION_CHANNEL_ID,
            "USB检测服务",
            "监听USB设备插拔和升级任务"
        )
        return createNotification(
            context,
            USB_DETECTION_CHANNEL_ID,
            "USB检测服务",
            "正在监听USB设备插拔"
        )
    }

    /**
     * 更新USB检测通知内容
     * @param context 上下文
     * @param content 通知内容
     */
    fun updateUsbDetectionNotification(context: Context, content: String) {
        updateNotification(
            context,
            USB_DETECTION_NOTIFICATION_ID,
            USB_DETECTION_CHANNEL_ID,
            "USB检测服务",
            content
        )
    }

    /**
     * 显示错误通知
     * @param context 上下文
     * @param errorMessage 错误信息
     */
    fun showErrorNotification(context: Context, errorMessage: String) {
        createNotificationChannel(
            context,
            ERROR_CHANNEL_ID,
            "错误通知",
            "显示系统错误信息",
            NotificationManager.IMPORTANCE_HIGH
        )
        updateNotification(
            context,
            ERROR_NOTIFICATION_ID,
            ERROR_CHANNEL_ID,
            "系统错误",
            errorMessage,
            android.R.drawable.ic_dialog_alert,
            isOngoing = false,
            autoCancel = true
        )
    }

    /**
     * 显示任务完成通知
     * @param context 上下文
     * @param taskName 任务名称
     */
    fun showCompletionNotification(context: Context, taskName: String) {
        createNotificationChannel(
            context,
            GENERAL_CHANNEL_ID,
            "任务状态",
            "显示任务完成信息",
            NotificationManager.IMPORTANCE_DEFAULT
        )
        val notification = createNotification(
            context,
            GENERAL_CHANNEL_ID,
            "任务状态",
            taskName,
            android.R.drawable.ic_dialog_info,
            isOngoing = false,
            autoCancel = true
        )
        showNotification(context, COMPLETION_NOTIFICATION_ID, notification)
    }

    /**
     * 清理所有应用相关的通知
     * @param context 上下文
     */
    fun clearAllAppNotifications(context: Context) {
        cancelNotification(context, USB_DETECTION_NOTIFICATION_ID)
        cancelNotification(context, ERROR_NOTIFICATION_ID)
        cancelNotification(context, COMPLETION_NOTIFICATION_ID)
    }
}
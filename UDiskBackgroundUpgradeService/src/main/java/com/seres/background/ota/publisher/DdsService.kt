package com.seres.background.ota.publisher

import android.content.Context
import android.system.Os.getenv
import com.seres.background.ota.utils.LogUtils
import com.seres.background.ota.utils.NotificationUtils
import com.seres.dds.sdk.DataReader
import com.seres.dds.sdk.DataWriter
import com.seres.dds.sdk.DomainParticipant
import com.seres.dds.sdk.Listener
import com.seres.dds.sdk.Publisher
import com.seres.dds.sdk.Subscriber
import com.seres.dds.sdk.Topic
import com.seres.dds.sdk.core.SubscriptionMatchedStatus
import seres.OTAM_UDiskSrv_Status_Topic.OTAM_UDiskSrv_Status
import seres.UDiskSrv_OTAM_Control_Topic.UDiskSrv_OTAM_Control

/** DDS服务类 负责使用DDS库实现S2S任务发布和资产信息订阅 */
class DdsService : Listener(1) {

    private val TAG = "DdsService"
    private var initialized = false
    private var context: Context? = null

    // DDS实体
    private var domainParticipant: DomainParticipant? = null

    // 发布者相关
    private var publisher: Publisher? = null
    private var upgradeTaskTopic: Topic? = null
    private var upgradeTaskWriter: DataWriter? = null

    // 订阅者相关
    private var subscriber: Subscriber? = null
    private var inventoryTopic: Topic? = null
    private var inventoryReader: DataReader? = null

    // 连接状态跟踪
    private var isDataReaderConnected = false
    private var connectedPublishersCount = 0

    // 回调接口
    private var inventoryInfoCallback: InventoryInfoCallback? = null
    /** 资产信息回调接口 */
    interface InventoryInfoCallback {
        fun onInventoryInfoReceived(inventoryInfoStatus: String)
    }

    // 重写 Listener 回调方法
    override fun on_subscription_matched(reader: Int, status: SubscriptionMatchedStatus) {
        super.on_subscription_matched(reader, status)
        LogUtils.i(TAG, "DataReader订阅匹配事件: current_count=${status.current_count}, " +
                "current_count_change=${status.current_count_change}, " +
                "total_count=${status.total_count}")

        // 更新连接状态
        val wasConnected = isDataReaderConnected
        isDataReaderConnected = status.current_count > 0
        connectedPublishersCount = status.current_count

        if (!wasConnected && isDataReaderConnected) {
            LogUtils.i(TAG, "DataReader已连接到 ${status.current_count} 个Publisher")
            context?.let {
                NotificationUtils.showCompletionNotification(it, "DDS DataReader已连接")
            }
        } else if (wasConnected && !isDataReaderConnected) {
            LogUtils.w(TAG, "DataReader连接已断开")
            context?.let {
                NotificationUtils.showErrorNotification(it, "DDS DataReader连接断开")
            }
        } else if (isDataReaderConnected) {
            LogUtils.d(TAG, "DataReader连接状态更新: ${status.current_count} 个Publisher")
        }
    }

    override fun on_data_available(reader: Int) {
        super.on_data_available(reader)
        LogUtils.d(TAG, "收到数据可用事件，reader: $reader")
        // 这里可以触发数据读取，替代定时轮询
        try {
            readInventoryData()
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理数据可用事件时出错: ${e.message}", e)
        }
    }

    /** 设置Context用于发送错误通知 */
    fun setContext(context: Context) {
        this.context = context
    }

    /** 初始化DDS服务 */
    fun initialize(): Boolean {
        return synchronized(this) {
            try {
                if (initialized) {
                    LogUtils.i(TAG, "DDS服务已初始化")
                    return true
                }
                LogUtils.i(TAG, "初始化DDS服务")
                // setenv(
                //         "CYCLONEDDS_URI",
                // "<CycloneDDS><Domainid=\"any\"><General><NetworkInterfaceAddress>eth0</NetworkInterfaceAddress></General></Domain></CycloneDDS>",
                //         true
                // )
                val envVal = getenv("CYCLONEDDS_URI")
                LogUtils.e(TAG, "ENV: CYCLONEDDS_URI=$envVal")
                // 创建DomainParticipant
                domainParticipant = DomainParticipant(42)
                LogUtils.i(TAG, "DomainParticipant创建成功")

                // 初始化发布者
                initializePublisher()

                // 初始化订阅者
                initializeSubscriber()

                initialized = true
                LogUtils.i(TAG, "DDS服务初始化完成")
                true
            } catch (e: Exception) {
                LogUtils.e(TAG, "DDS服务初始化失败: ${e.message}", e)
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(it, "DDS服务初始化失败: ${e.message}")
                }
                false
            }
        }
    }

    /** 初始化发布者 */
    private fun initializePublisher() {
        // 创建Publisher
        publisher = Publisher(domainParticipant!!)
        LogUtils.i(TAG, "Publisher创建成功")

        // 创建升级任务Topic和DataWriter
        upgradeTaskTopic =
                Topic(domainParticipant!!, "UDiskSrv_OTAM_Control", UDiskSrv_OTAM_Control())
        upgradeTaskWriter = DataWriter(publisher!!, upgradeTaskTopic!!)
        LogUtils.i(TAG, "升级任务Topic和DataWriter创建成功")
    }

    /** 初始化订阅者 */
    private fun initializeSubscriber() {
        // 创建Subscriber
        subscriber = Subscriber(domainParticipant!!)
        LogUtils.i(TAG, "Subscriber创建成功")
        // 创建资产信息Topic和DataReader，并设置listener
        inventoryTopic = Topic(domainParticipant!!, "OTAM_UDiskSrv_Status", OTAM_UDiskSrv_Status())
        inventoryReader = DataReader(subscriber!!, inventoryTopic!!, null,1)
        LogUtils.i(TAG, "资产信息Topic和DataReader创建成功，已设置Listener")
    }



    /** 读取资产信息数据 */
    private fun readInventoryData() {
        inventoryReader?.let { reader ->
            val samples = reader.take(1) // 一次最多读取10个样本

            if (samples.sample_count > 0) {
                LogUtils.d(TAG, "收到 ${samples.sample_count} 个资产信息样本")
                samples.sample_list?.forEach { sample ->
                    sample.info?.let { info ->
                        if (info.valid_data) {
                            val inventoryStatus = sample.type as OTAM_UDiskSrv_Status
                            val inventoryInfoStatus = inventoryStatus.inventory_info

                            LogUtils.d(TAG, "DDS Subscriber收到资产信息")

                            // 通过回调通知上层
                            inventoryInfoCallback?.onInventoryInfoReceived(inventoryInfoStatus)
                        }
                    }
                }
            }
        }
    }

    /** 发布升级任务信息 */
    fun publishUpgradeTaskInfo(taskJson: String): Boolean {
        return try {
            LogUtils.i(TAG, "准备发布升级任务信息")

            // 检查DDS服务是否已初始化
            if (!initialized) {
                LogUtils.e(TAG, "DDS服务未初始化，无法发布升级任务信息")
                context?.let {
                    NotificationUtils.showErrorNotification(it, "DDS服务未初始化")
                }
                return false
            }

            // 检查DataReader是否已连接
            if (!isDataReaderConnected) {
                LogUtils.e(TAG, "DataReader未连接到任何Publisher，无法发布升级任务信息")
                context?.let {
                    NotificationUtils.showErrorNotification(it, "DataReader未连接，无法发布消息")
                }
                return false
            }

            LogUtils.i(TAG, "发布升级任务信息: $taskJson")
            val upgradeTaskInfo =
                    UDiskSrv_OTAM_Control
                            .descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_INFO()
            upgradeTaskInfo.upgrade_task_info = taskJson
            // 发布数据
            val result = upgradeTaskWriter?.write(upgradeTaskInfo)

            if (result == 0) {
                LogUtils.i(TAG, "升级任务信息发布成功")
                true
            } else {
                LogUtils.e(TAG, "升级任务信息发布失败，错误码: $result")
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(it, "升级任务信息发布失败，错误码: $result")
                }
                false
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "发布升级任务信息时出错: ${e.message}", e)
            // 发送错误通知
            context?.let { NotificationUtils.showErrorNotification(it, "发布升级任务信息异常: ${e.message}") }
            false
        }
    }

    /** 发布升级任务匹配结果 */
    fun publishUpgradeTaskMatch(isMatch: Boolean): Boolean {
        return try {
            LogUtils.i(TAG, "准备发布升级任务匹配结果: $isMatch")

            // 检查DDS服务是否已初始化
            if (!initialized) {
                LogUtils.e(TAG, "DDS服务未初始化，无法发布升级任务匹配结果")
                context?.let {
                    NotificationUtils.showErrorNotification(it, "DDS服务未初始化")
                }
                return false
            }

            // 检查DataReader是否已连接
            if (!isDataReaderConnected) {
                LogUtils.e(TAG, "DataReader未连接到任何Publisher，无法发布升级任务匹配结果")
                context?.let {
                    NotificationUtils.showErrorNotification(it, "DataReader未连接，无法发布消息")
                }
                return false
            }

            LogUtils.i(TAG, "发布升级任务匹配结果: $isMatch")

            // 创建UpgradeTaskIsMatchDescriptor实例
            val upgradeTaskMatch =
                    UDiskSrv_OTAM_Control
                            .descriptor_seres_UDiskSrv_OTAM_Control_Topic_UDiskSrvOtamCtrlType_UPGRADE_TASK_IS_MATCH()
            upgradeTaskMatch.upgrade_task_is_match = isMatch

            // 发布数据
            val result = upgradeTaskWriter?.write(upgradeTaskMatch) ?: -1

            if (result == 0) {
                LogUtils.i(TAG, "升级任务匹配结果发布成功")
                true
            } else {
                LogUtils.e(TAG, "升级任务匹配结果发布失败，错误码: $result")
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(it, "升级任务匹配结果发布失败，错误码: $result")
                }
                false
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "发布升级任务匹配结果时出错: ${e.message}", e)
            // 发送错误通知
            context?.let {
                NotificationUtils.showErrorNotification(it, "发布升级任务匹配结果异常: ${e.message}")
            }
            false
        }
    }

    /** 设置资产信息回调 */
    fun setInventoryInfoCallback(callback: InventoryInfoCallback) {
        this.inventoryInfoCallback = callback
    }

    /** 检查DDS连接状态 */
    fun isConnected(): Boolean {
        return domainParticipant != null &&
                publisher != null &&
                upgradeTaskWriter != null &&
                subscriber != null &&
                inventoryReader != null
    }

    /** 清理资源 */
    fun cleanup() {
        try {
            LogUtils.i(TAG, "清理DDS资源")

            // 清理DataReader
            inventoryReader = null

            // 清理DataWriter
            upgradeTaskWriter = null

            // 清理Topic
            inventoryTopic = null
            upgradeTaskTopic = null

            // 清理Publisher和Subscriber
            publisher = null
            subscriber = null

            // 清理DomainParticipant
            domainParticipant = null

            // 重置初始化状态和连接状态
            initialized = false
            isDataReaderConnected = false
            connectedPublishersCount = 0

            LogUtils.i(TAG, "DDS资源清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理DDS资源时出错: ${e.message}", e)
        }
    }
}

package com.seres.background.ota.service

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Environment
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import com.seres.background.ota.utils.LogUtils
import java.io.File
import java.net.URI
import java.util.Hashtable

class UsbDetectionReceiver private constructor(private val context: Context) {

    companion object {
        private const val ENABLE_MEDIA_SERVICE_BROADCAST = false
        private const val COOL_DOWN_TIME = 8 * 1000L // ms
        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var instance: UsbDetectionReceiver? = null

        fun getInstance(context: Context): UsbDetectionReceiver {
            return instance ?: synchronized(this) {
                instance ?: UsbDetectionReceiver(context.applicationContext).also { instance = it }
            }
        }
    }

    private val TAG = this::class.java.simpleName
    private val mountedStorages = Hashtable<String, File>()
    private val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
    private val lock = Any()

    private var isInCoolDown = false
    private var startDetectTime: Long = 0
    private var detectionCallback: DetectionCallback? = null

    // StorageVolume 回调（Android 7.0+）
    private val storageVolumeCallback = object : StorageManager.StorageVolumeCallback() {
        override fun onStateChanged(volume: StorageVolume) {
            val uuid = volume.mediaStoreVolumeName
            val state = volume.state

            // 忽略主存储
            if (volume.isPrimary) return

            // 冷却时间过滤
            if (isInCoolDown) {
                val curTime = System.currentTimeMillis()
                if (curTime - startDetectTime < COOL_DOWN_TIME) {
                    return
                } else {
                    isInCoolDown = false
                }
            }

            LogUtils.d(TAG, "uuid: $uuid state: $state")

            synchronized(lock) {
                when (state) {
                    Environment.MEDIA_MOUNTED -> {
                        if (!mountedStorages.containsKey(uuid)) {
                            mountedStorages[uuid] = volume.directory
                            detectionCallback?.onDeviceAttached(mountedStorages[uuid]!!)
                        }
                    }
                    Environment.MEDIA_EJECTING -> {
                        if (mountedStorages.containsKey(uuid)) {
                            detectionCallback?.onDeviceDetached(mountedStorages[uuid]!!)
                            mountedStorages.remove(uuid)
                        }
                    }
                }
            }
        }
    }

    // 动态广播接收器（旧 API）
    private val mediaReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action ?: return
            val path = intent.dataString ?: return

            LogUtils.d(TAG, "action: $action path: $path")

            // 过滤 emulated 存储
            if (path.contains("emulated")) return

            synchronized(lock) {
                val rootDir = File(URI.create(path))
                val uuid = rootDir.name.lowercase()
                when (action) {
                    Intent.ACTION_MEDIA_MOUNTED -> {
                        if (!mountedStorages.containsKey(uuid)) {
                            mountedStorages[uuid] = rootDir
                            detectionCallback?.onDeviceAttached(mountedStorages[uuid]!!)
                        }
                    }
                    Intent.ACTION_MEDIA_EJECT-> {
                        detectionCallback?.onDeviceDetached(mountedStorages[uuid])
                        mountedStorages.remove(uuid)
                    }
                }
                true
            }
        }
    }

    private fun registerMediaReceiver() {
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_MEDIA_MOUNTED)
            addAction(Intent.ACTION_MEDIA_EJECT)
            addDataScheme("file")
        }
        context.registerReceiver(mediaReceiver, filter)
    }

    fun startDetection(callback: DetectionCallback) {
        detectionCallback = callback
        mountedStorages.clear()
        isInCoolDown = true
        startDetectTime = System.currentTimeMillis()

        if (ENABLE_MEDIA_SERVICE_BROADCAST) {
            registerMediaReceiver()
        }
        storageManager.registerStorageVolumeCallback({ runnable -> runnable.run() }, storageVolumeCallback)
    }

    fun stopDetection() {
        if (ENABLE_MEDIA_SERVICE_BROADCAST) {
            context.unregisterReceiver(mediaReceiver)
        }
        storageManager.unregisterStorageVolumeCallback(storageVolumeCallback)
        mountedStorages.clear()
        detectionCallback = null
        instance = null
    }

    fun hasMountedUsb(): Boolean {
        return mountedStorages.isNotEmpty()
    }

    interface DetectionCallback {
        fun onDeviceAttached(usbPath: File)
        fun onDeviceDetached(usbPath: File?)
    }
}

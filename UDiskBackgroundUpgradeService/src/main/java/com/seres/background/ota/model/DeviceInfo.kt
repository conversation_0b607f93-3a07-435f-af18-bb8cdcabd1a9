package com.seres.background.ota.model

import com.google.gson.annotations.SerializedName

/**
 * 升级任务信息根对象
 */
data class UpgradeTaskInfo(
    val edition: Edition = Edition(),
    @SerializedName("android_shared_path")
    val androidSharedPath: String = "",
    @SerializedName("yocto_shared_path")
    val yoctoSharedPath: String = "",
    val dpPackage: DpPackage = DpPackage()
)

/**
 * 版本信息
 */
data class Edition(
    val prettyVersion: String = ""
)

/**
 * DP包信息
 */
data class DpPackage(
    val vin: String = "",
    @SerializedName("pkg_id")
    val pkgId: String = "",
    @SerializedName("pkg_info")
    val pkgInfo: PkgInfo = PkgInfo()
)

/**
 * 包信息
 */
data class PkgInfo(
    @SerializedName("pkg_ver")
    val pkgVer: String = "",
    @SerializedName("dc_list")
    val dcList: List<DcItem> = emptyList(),
    @SerializedName("ecu_list")
    val ecuList: List<EcuItem> = emptyList(),
    val rules: Rules = Rules(),
    @SerializedName("upgrade_order")
    val upgradeOrder: UpgradeOrder = UpgradeOrder()
)

/**
 * DC项（升级包文件信息）
 */
data class DcItem(
    val domain: Int = 0,
    var path: String = "",
    @SerializedName("zip_size_in_byte")
    val zipSizeInByte: Long = 0,
    @SerializedName("node_addr")
    val nodeAddr: String = "",
    @SerializedName("soft_ver")
    val softVer: String = "",
    @SerializedName("pkg_type")
    val pkgType: Int = 0,
    val sha256: String = ""
)

/**
 * ECU项
 */
data class EcuItem(
    val domain: Int = 0,
    @SerializedName("ecu_name")
    val ecuName: String = "",
    @SerializedName("ecu_id")
    val ecuId: String = "",
    @SerializedName("node_addr")
    val nodeAddr: String = "",
    val seamless: Boolean = false
)

/**
 * 规则
 */
data class Rules(
    val version: Int = 1,
    val expr: Expr = Expr()
)

/**
 * 表达式
 */
data class Expr(
    @SerializedName("pre_install")
    val preInstall: PreInstall = PreInstall()
)

/**
 * 预安装条件
 */
data class PreInstall(
    val elms: List<Element> = emptyList()
)

/**
 * 条件元素
 */
data class Element(
    val `var`: String = "",
    val `val`: String = ""
)

/**
 * 升级顺序
 */
data class UpgradeOrder(
    val install: OrderStage = OrderStage(),
    val rollback: OrderStage = OrderStage(),
    val active: OrderStage = OrderStage()
)

/**
 * 顺序阶段
 */
data class OrderStage(
    val stages: List<Stage> = emptyList()
)

/**
 * 阶段
 */
data class Stage(
    @SerializedName("voltage_type")
    val voltageType: Int = 1,
    @SerializedName("order_node_addr")
    val orderNodeAddr: List<List<String>> = emptyList()
)


/**
 * 升级任务信息（管理器使用）
 */
data class UpgradeTaskManagerInfo(
    val taskId: String,
    val upgradeTaskInfo: UpgradeTaskInfo? = null,
    val usbPath: String,
    val status: TaskStatus,
    val validPackages: List<DcItem> = emptyList(),
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis()
)

/**
 * 任务状态枚举
 */
enum class TaskStatus {
    PENDING,    // 等待中
    COPYING,    // 拷贝文件中
    READY,      // 准备就绪
    PUBLISHED,  // 已发布
    FAILED,     // 失败
    CANCELLED   // 已取消
}

/**
 * 资产信息数据类
 */
data class InventoryInfo(
    @SerializedName("node_addr") val nodeAddr: String = "",
    @SerializedName("part_number") val partNumber: String = "",
    @SerializedName("software_version") val softwareVersion: String = "",
    @SerializedName("supplier_code") val supplierCode: String = "",
    @SerializedName("ecu_name") val ecuName: String = "",
    @SerializedName("serial_number") val serialNumber: String = "",
    @SerializedName("hardware_version") val hardwareVersion: String = "",
    @SerializedName("bootloader_version") val bootloaderVersion: String = "",
    @SerializedName("backup_version") val backupVersion: String = ""
)

data class InventoryInfoList(
    @SerializedName("inventory_list") val inventoryInfoList: List<InventoryInfo> = emptyList()
)

package com.seres.background.ota.utils

import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.Properties

/**
 * 配置文件管理工具类
 * 提供配置文件的读取、写入和管理功能
 */
object ConfigManager {
    
    private const val TAG = "ConfigManager"
    private const val CONFIG_FILE_NAME = "upgrade_config.properties"
    
    // 配置文件搜索路径（按优先级排序）
    private val CONFIG_PATHS = listOf(
        "/data/local/tmp/$CONFIG_FILE_NAME",           // 临时目录（可写）
        "/system/etc/$CONFIG_FILE_NAME",               // 系统配置目录
        "/vendor/etc/$CONFIG_FILE_NAME",               // 厂商配置目录
        "/mnt/vendor/$CONFIG_FILE_NAME"                // 挂载目录
    )
    
    /**
     * 读取配置值
     */
    fun getConfig(key: String, defaultValue: String = ""): String {
        for (configPath in CONFIG_PATHS) {
            try {
                val configFile = File(configPath)
                if (configFile.exists() && configFile.canRead()) {
                    val properties = Properties()
                    FileInputStream(configFile).use { fis ->
                        properties.load(fis)
                    }
                    
                    val value = properties.getProperty(key, "").trim()
                    if (value.isNotEmpty()) {
                        LogUtils.d(TAG, "从配置文件读取 $key = $value (文件: $configPath)")
                        return value
                    }
                }
            } catch (e: Exception) {
                LogUtils.w(TAG, "读取配置文件失败: $configPath - ${e.message}")
            }
        }
        
        LogUtils.d(TAG, "未找到配置 $key，使用默认值: $defaultValue")
        return defaultValue
    }
    
    /**
     * 设置配置值
     */
    fun setConfig(key: String, value: String): Boolean {
        return try {
            // 使用第一个路径（可写目录）
            val configFile = File(CONFIG_PATHS[0])
            
            val properties = Properties()
            
            // 如果文件存在，先读取现有配置
            if (configFile.exists()) {
                FileInputStream(configFile).use { fis ->
                    properties.load(fis)
                }
            }
            
            // 设置新值
            properties.setProperty(key, value)
            properties.setProperty("# 更新时间", System.currentTimeMillis().toString())
            
            // 确保父目录存在
            configFile.parentFile?.mkdirs()
            
            // 保存配置文件
            FileOutputStream(configFile).use { fos ->
                properties.store(fos, "Configuration updated by ConfigManager")
            }
            
            LogUtils.i(TAG, "配置更新成功: $key = $value")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "更新配置失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 获取拷贝路径
     */
    fun getCopyPath(): String {
        return getConfig("copy_path", "/mnt/vendor/yocto_data")
    }
    
    /**
     * 设置拷贝路径
     */
    fun setCopyPath(path: String): Boolean {
        if (path.trim().isEmpty()) {
            LogUtils.e(TAG, "拷贝路径不能为空")
            return false
        }
        
        return setConfig("copy_path", path.trim())
    }
    
    /**
     * 获取当前有效的配置文件路径
     */
    fun getCurrentConfigFile(): String? {
        for (configPath in CONFIG_PATHS) {
            val configFile = File(configPath)
            if (configFile.exists() && configFile.canRead()) {
                return configPath
            }
        }
        return null
    }
    
    /**
     * 创建默认配置文件
     */
    fun createDefaultConfig(): Boolean {
        return try {
            val configFile = File(CONFIG_PATHS[0])
            
            val properties = Properties()
            properties.setProperty("copy_path", "/mnt/vendor/yocto_data")
            properties.setProperty("# 说明", "copy_path: 升级文件拷贝目标路径")
            properties.setProperty("# 示例", "copy_path=/mnt/vendor/yocto_data")
            properties.setProperty("# 创建时间", System.currentTimeMillis().toString())
            
            configFile.parentFile?.mkdirs()
            FileOutputStream(configFile).use { fos ->
                properties.store(fos, "Default Upgrade Configuration")
            }
            
            LogUtils.i(TAG, "创建默认配置文件成功: ${configFile.absolutePath}")
            true
        } catch (e: Exception) {
            LogUtils.e(TAG, "创建默认配置文件失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 列出所有配置
     */
    fun listAllConfigs(): Map<String, String> {
        val configs = mutableMapOf<String, String>()
        
        for (configPath in CONFIG_PATHS) {
            try {
                val configFile = File(configPath)
                if (configFile.exists() && configFile.canRead()) {
                    val properties = Properties()
                    FileInputStream(configFile).use { fis ->
                        properties.load(fis)
                    }
                    
                    properties.forEach { key, value ->
                        if (!key.toString().startsWith("#")) {
                            configs[key.toString()] = value.toString()
                        }
                    }
                    break // 只读取第一个找到的配置文件
                }
            } catch (e: Exception) {
                LogUtils.w(TAG, "读取配置文件失败: $configPath - ${e.message}")
            }
        }
        
        return configs
    }
}

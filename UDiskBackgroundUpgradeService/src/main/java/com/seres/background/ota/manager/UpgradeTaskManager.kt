package com.seres.background.ota.manager

import android.content.Context
import com.google.gson.Gson
import com.seres.background.ota.model.*
import com.seres.background.ota.publisher.DdsService
import com.seres.background.ota.utils.LogUtils
import com.seres.background.ota.utils.NotificationUtils
import com.seres.background.ota.utils.ConfigManager
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.MessageDigest
import java.util.concurrent.Executors
import java.util.Properties

/**
 * 升级任务管理器（单任务版）
 */
class UpgradeTaskManager {

    private val TAG = "UpgradeTaskManager"
    private val gson = Gson()
    private val threadPool = Executors.newSingleThreadExecutor()
    private var ddsService: DdsService? = null
    private var context: Context? = null
    private var currentTask: UpgradeTaskManagerInfo? = null

    // 配置文件相关
    private val defaultSharePath = "/mnt/vendor/yocto_data"

    // 文件检查策略配置
    enum class FileCheckStrategy {
        NONE,           // 不检查，直接覆盖
        SIZE_ONLY,      // 仅检查大小
        FULL_HASH       // 完整哈希检查
    }

    private val fileCheckStrategy = FileCheckStrategy.SIZE_ONLY // 默认使用快速检查

    fun setDdsService(service: DdsService) {
        this.ddsService = service
    }

    /**
     * 初始化配置文件（如果不存在则创建默认配置）
     */
    fun initializeConfig() {
        LogUtils.i(TAG, "初始化配置文件")

        // 检查是否存在配置文件
        if (ConfigManager.getCurrentConfigFile() == null) {
            LogUtils.i(TAG, "未找到配置文件，创建默认配置")
            ConfigManager.createDefaultConfig()
        }

        // 验证当前配置
        val currentPath = ConfigManager.getCopyPath()
        LogUtils.i(TAG, "配置初始化完成，当前拷贝路径: $currentPath")
    }

    fun setContext(context: Context) {
        this.context = context
    }

    /**
     * 更新配置文件中的拷贝路径
     */
    fun updateCopyPath(newPath: String): Boolean {
        val success = ConfigManager.setCopyPath(newPath)
        if (success) {
            LogUtils.i(TAG, "拷贝路径更新成功: $newPath")

            // 验证新路径是否可用
            val testDir = File(newPath)
            if (!testDir.exists()) {
                LogUtils.w(TAG, "新路径不存在，尝试创建: $newPath")
                testDir.mkdirs()
            }
        }
        return success
    }

    /**
     * 获取当前有效的配置文件路径
     */
    fun getCurrentConfigFilePath(): String? {
        return ConfigManager.getCurrentConfigFile()
    }

    /**
     * 处理升级任务信息（同一时间只允许一个任务，启动新任务会取消旧任务）
     */
    fun processUpgradeTaskInfo(
        upgradeTaskInfo: UpgradeTaskInfo,
        usbDir: File,
        validPackages: List<DcItem>) {
        // 先取消当前任务
        cancelCurrentTask()

        val taskId = generateTaskId()
        val taskInfo = UpgradeTaskManagerInfo(
            taskId = taskId,
            upgradeTaskInfo = upgradeTaskInfo,
            usbPath = usbDir.absolutePath,
            status = TaskStatus.PENDING,
            validPackages = validPackages
        )

        currentTask = taskInfo
        LogUtils.i(TAG, "开始处理升级任务信息: $taskId，包含 ${validPackages.size} 个升级包")

        threadPool.execute {
            try {
                updateTaskStatus(TaskStatus.COPYING)
                context?.let {
                    NotificationUtils.showCompletionNotification(
                        it,
                        "文件拷贝中..."
                    )
                }
                val success = copyUpgradeFilesFromTaskInfo(usbDir, validPackages)

                val taskJson = gson
                    .newBuilder()
                    .setPrettyPrinting()
                    .create()
                    .toJson(upgradeTaskInfo)

                if (success) {
                    updateTaskStatus(TaskStatus.READY)
                    publishTaskToDds(taskJson)
                    LogUtils.i(TAG, "升级任务信息处理完成，成功拷贝文件和发布任务: $taskId")
                } else {
                    updateTaskStatus(TaskStatus.FAILED)
                    LogUtils.e(TAG, "升级任务文件拷贝失败，JSON已保存: $taskId")
                    // 发送错误通知
                    context?.let {
                        NotificationUtils.showErrorNotification(
                            it,
                            "升级文件拷贝失败"
                        )
                    }
                }
            } catch (e: Exception) {
                updateTaskStatus(TaskStatus.FAILED)
                LogUtils.e(TAG, "处理升级任务失败: $taskId - ${e.message}", e)
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(
                        it,
                        "处理升级任务异常: ${e.message}"
                    )
                }
            }
        }
    }

    private fun copyUpgradeFilesFromTaskInfo(usbDir: File, validPackages: List<DcItem>): Boolean {
        return try {
            // 每次拷贝时重新读取配置文件
            val currentCopyPath = ConfigManager.getCopyPath()
            LogUtils.i(TAG, "开始拷贝升级文件到: $currentCopyPath")

            val possibleDirs = listOf(
                currentCopyPath,
                defaultSharePath  // 备用路径
            ).filter { it.isNotEmpty() }

            var shareDir: File? = null

            for (dirPath in possibleDirs) {
                val testDir = File(dirPath)
                try {
                    if (!testDir.exists()) testDir.mkdirs()
                    val testFile = File(testDir, "test_write_permission.tmp")
                    testFile.writeText("test")
                    testFile.delete()
                    shareDir = testDir
                    LogUtils.i(TAG, "使用目标目录: ${testDir.absolutePath}")
                    break
                } catch (e: Exception) {
                    LogUtils.w(TAG, "目录 $dirPath 不可用: ${e.message}")
                }
            }

            if (shareDir == null) {
                LogUtils.e(TAG, "无法找到可用的目标拷贝目录")
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(
                        it,
                        "无法找到可用的目标拷贝目录"
                    )
                }
                return false
            }

            var successCount = 0
            val totalFiles = validPackages.size
            LogUtils.i(TAG, "准备拷贝 $totalFiles 个升级文件")

            for ((index, dcItem) in validPackages.withIndex()) {
                val currentFileNum = index + 1
                val sourceFileName = extractFileNameFromPath(dcItem.path)
                LogUtils.i(TAG, "正在处理文件 $currentFileNum/$totalFiles: $sourceFileName")

                val sourceFile = findFileInUsbDir(usbDir, sourceFileName)
                if (sourceFile != null && sourceFile.exists()) {
                    try {
                        val targetFile = File(shareDir, sourceFileName)

                        // 检查目标文件是否已存在且内容相同
                        if (targetFile.exists() && fileCheckStrategy != FileCheckStrategy.NONE) {
                            LogUtils.i(TAG, "目标文件已存在，使用${fileCheckStrategy}策略检查: ${targetFile.absolutePath}")
                            if (shouldSkipCopy(sourceFile, targetFile)) {
                                LogUtils.i(TAG, "文件检查通过，跳过拷贝 ($currentFileNum/$totalFiles): ${sourceFile.name}")
                                successCount++
                                continue
                            } else {
                                LogUtils.i(TAG, "文件不同，将覆盖目标文件: ${targetFile.absolutePath}")
                            }
                        }

                        // 执行文件拷贝
                        copyFile(sourceFile, targetFile)
                        successCount++
                        LogUtils.i(TAG, "文件拷贝成功 ($currentFileNum/$totalFiles): ${sourceFile.name} -> ${targetFile.absolutePath}")
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "拷贝文件失败 ($currentFileNum/$totalFiles) ${sourceFile.name}: ${e.message}")
                    }
                } else {
                    LogUtils.e(TAG, "源文件不存在 ($currentFileNum/$totalFiles): $sourceFileName")
                }
            }

            LogUtils.i(TAG, "文件拷贝完成，成功: $successCount/$totalFiles")

            successCount > 0
        } catch (e: Exception) {
            LogUtils.e(TAG, "拷贝升级文件失败: ${e.message}", e)
            // 发送错误通知
            context?.let {
                NotificationUtils.showErrorNotification(
                    it,
                    "拷贝升级文件异常: ${e.message}"
                )
            }
            false
        }
    }

    private fun copyFile(source: File, target: File) {
        val totalSize = source.length()
        var copiedSize = 0L
        var lastLogTime = System.currentTimeMillis()
        val logInterval = 2000L // 每2秒记录一次进度

        LogUtils.i(TAG, "开始拷贝文件: ${source.name} (${formatFileSize(totalSize)})")

        FileInputStream(source).use { input ->
            FileOutputStream(target).use { output ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    output.write(buffer, 0, bytesRead)
                    copiedSize += bytesRead

                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastLogTime >= logInterval || copiedSize == totalSize) {
                        val progress = if (totalSize > 0) (copiedSize * 100 / totalSize) else 0
                        LogUtils.d(TAG, "拷贝进度: ${source.name} - ${progress}% (${formatFileSize(copiedSize)}/${formatFileSize(totalSize)})")
                        lastLogTime = currentTime
                    }
                }
                output.flush()
            }
        }

        LogUtils.i(TAG, "文件拷贝完成: ${source.name} -> ${target.absolutePath}")
    }

    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }

    /**
     * 计算文件的快速哈希值（使用MD5，速度更快）
     */
    private fun calculateFileHash(file: File): String? {
        return try {
            val digest = MessageDigest.getInstance("MD5")
            FileInputStream(file).use { fis ->
                val buffer = ByteArray(64 * 1024) // 增大缓冲区到64KB
                var bytesRead: Int
                while (fis.read(buffer).also { bytesRead = it } != -1) {
                    digest.update(buffer, 0, bytesRead)
                }
            }
            // 将字节数组转换为十六进制字符串
            digest.digest().joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            LogUtils.e(TAG, "计算文件哈希值失败: ${file.name} - ${e.message}", e)
            null
        }
    }

    /**
     * 快速检查文件头部内容（前1KB）
     */
    private fun compareFileHeaders(sourceFile: File, targetFile: File): Boolean {
        return try {
            val headerSize = minOf(1024, sourceFile.length().toInt(), targetFile.length().toInt())
            val sourceHeader = ByteArray(headerSize)
            val targetHeader = ByteArray(headerSize)

            FileInputStream(sourceFile).use { it.read(sourceHeader) }
            FileInputStream(targetFile).use { it.read(targetHeader) }

            sourceHeader.contentEquals(targetHeader)
        } catch (e: Exception) {
            LogUtils.w(TAG, "比较文件头部失败: ${sourceFile.name} - ${e.message}")
            false
        }
    }

    /**
     * 根据策略决定是否跳过拷贝
     */
    private fun shouldSkipCopy(sourceFile: File, targetFile: File): Boolean {
        return when (fileCheckStrategy) {
            FileCheckStrategy.NONE -> false
            FileCheckStrategy.SIZE_ONLY -> sourceFile.length() == targetFile.length()
            FileCheckStrategy.FULL_HASH -> areFilesIdenticalFull(sourceFile, targetFile)
        }
    }

    /**
     * 完整哈希比较（最准确但较慢）
     */
    private fun areFilesIdenticalFull(sourceFile: File, targetFile: File): Boolean {
        return try {
            val startTime = System.currentTimeMillis()

            // 检查文件大小
            if (sourceFile.length() != targetFile.length()) {
                LogUtils.d(TAG, "文件大小不同: ${sourceFile.name}")
                return false
            }

            // 检查最后修改时间
            if (sourceFile.lastModified() == targetFile.lastModified()) {
                LogUtils.d(TAG, "文件大小和修改时间相同: ${sourceFile.name}")
                return true
            }

            // 先比较文件头部，避免不必要的哈希计算
            if (!compareFileHeaders(sourceFile, targetFile)) {
                LogUtils.d(TAG, "文件头部不同: ${sourceFile.name}")
                return false
            }

            // 计算完整哈希值
            LogUtils.d(TAG, "计算哈希值: ${sourceFile.name} (${formatFileSize(sourceFile.length())})")
            val sourceHash = calculateFileHash(sourceFile)
            val targetHash = calculateFileHash(targetFile)

            if (sourceHash == null || targetHash == null) {
                LogUtils.w(TAG, "无法计算文件哈希值: ${sourceFile.name}")
                return false
            }

            val identical = sourceHash == targetHash
            val elapsedTime = System.currentTimeMillis() - startTime

            LogUtils.d(TAG, "哈希比较完成: ${sourceFile.name} - ${if (identical) "相同" else "不同"} (耗时: ${elapsedTime}ms)")
            identical
        } catch (e: Exception) {
            LogUtils.e(TAG, "完整比较文件失败: ${sourceFile.name} - ${e.message}", e)
            false
        }
    }

    private fun publishTaskToDds(taskJson: String) {
        val success = ddsService?.publishUpgradeTaskInfo(taskJson) ?: false
        if (success) {
            updateTaskStatus(TaskStatus.PUBLISHED)
            context?.let {
                NotificationUtils.showCompletionNotification(
                    it,
                    "升级任务发布"
                )
            }
        } else {
            updateTaskStatus(TaskStatus.FAILED)
        }
    }

    private fun updateTaskStatus(status: TaskStatus) {
        currentTask?.let {
            currentTask = it.copy(status = status)
            LogUtils.d(TAG, "任务状态更新: ${it.taskId} -> $status")
        }
    }

    fun cancelCurrentTask() {
        currentTask?.let {
            updateTaskStatus(TaskStatus.CANCELLED)
            LogUtils.i(TAG, "已取消任务: ${it.taskId}")
            currentTask = null
        }
    }

    private fun generateTaskId(): String {
        return "UPGRADE_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    private fun extractFileNameFromPath(path: String): String {
        return if (path.contains("/")) {
            // 如果路径中包含 /，提取最后的文件名部分
            path.substringAfterLast("/")
        } else {
            // 如果路径中不包含 /，直接返回原字符串
            path
        }
    }

    private fun findFileInUsbDir(usbDir: File, fileName: String): File? {
        return try {
            val allFiles = getAllFilesRecursive(usbDir)
            allFiles.find { file ->
                file.name.equals(fileName, ignoreCase = true) ||
                        file.name.contains(fileName, ignoreCase = true)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "查找文件失败: $fileName - ${e.message}")
            null
        }
    }

    private fun getAllFilesRecursive(dir: File): List<File> {
        val files = mutableListOf<File>()
        try {
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(file)
                } else if (file.isDirectory) {
                    files.addAll(getAllFilesRecursive(file))
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "扫描目录失败: ${dir.absolutePath} - ${e.message}")
        }
        return files
    }
}

package com.seres.background.ota.manager

import android.content.Context
import com.google.gson.Gson
import com.seres.background.ota.model.*
import com.seres.background.ota.publisher.DdsService
import com.seres.background.ota.utils.LogUtils
import com.seres.background.ota.utils.NotificationUtils
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.Executors

/**
 * 升级任务管理器（单任务版）
 */
class UpgradeTaskManager {

    private val TAG = "UpgradeTaskManager"
    private val gson = Gson()
    private val threadPool = Executors.newSingleThreadExecutor()
    private var ddsService: DdsService? = null
    private var context: Context? = null
    private var currentTask: UpgradeTaskManagerInfo? = null

    // 默认Android共享路径
    private val defaultAndroidSharePath = "/mnt/vendor/yocto_data"

    fun setDdsService(service: DdsService) {
        this.ddsService = service
    }

    fun setContext(context: Context) {
        this.context = context
    }

    /**
     * 处理升级任务信息（同一时间只允许一个任务，启动新任务会取消旧任务）
     */
    fun processUpgradeTaskInfo(
        upgradeTaskInfo: UpgradeTaskInfo,
        usbDir: File,
        validPackages: List<DcItem>) {
        // 先取消当前任务
        cancelCurrentTask()

        val taskId = generateTaskId()
        val taskInfo = UpgradeTaskManagerInfo(
            taskId = taskId,
            upgradeTaskInfo = upgradeTaskInfo,
            usbPath = usbDir.absolutePath,
            status = TaskStatus.PENDING,
            validPackages = validPackages
        )

        currentTask = taskInfo
        LogUtils.i(TAG, "开始处理升级任务信息: $taskId，包含 ${validPackages.size} 个升级包")

        threadPool.execute {
            try {
                updateTaskStatus(TaskStatus.COPYING)
                context?.let {
                    NotificationUtils.showCompletionNotification(
                        it,
                        "文件拷贝中..."
                    )
                }
                val success = copyUpgradeFilesFromTaskInfo(upgradeTaskInfo, usbDir, validPackages)

                // 生成发送给DDS的JSON（移除共享路径字段）
                val taskJson = createDdsTaskJson(upgradeTaskInfo)

                if (success) {
                    updateTaskStatus(TaskStatus.READY)
                    publishTaskToDds(taskJson)
                    LogUtils.i(TAG, "升级任务信息处理完成，成功拷贝文件和发布任务: $taskId")
                } else {
                    updateTaskStatus(TaskStatus.FAILED)
                    LogUtils.e(TAG, "升级任务文件拷贝失败，JSON已保存: $taskId")
                    // 发送错误通知
                    context?.let {
                        NotificationUtils.showErrorNotification(
                            it,
                            "升级文件拷贝失败"
                        )
                    }
                }
            } catch (e: Exception) {
                updateTaskStatus(TaskStatus.FAILED)
                LogUtils.e(TAG, "处理升级任务失败: $taskId - ${e.message}", e)
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(
                        it,
                        "处理升级任务异常: ${e.message}"
                    )
                }
            }
        }
    }

    private fun copyUpgradeFilesFromTaskInfo(
        upgradeTaskInfo: UpgradeTaskInfo,
        usbDir: File,
        validPackages: List<DcItem>
    ): Boolean {
        return try {
            // 从JSON中获取android_shared_path，如果为空则使用默认路径
            val androidSharePath = upgradeTaskInfo.androidSharedPath.takeIf { it.isNotEmpty() }
                ?: defaultAndroidSharePath

            LogUtils.i(TAG, "开始拷贝升级文件到: $androidSharePath")
            LogUtils.d(TAG, "使用的Android共享路径: $androidSharePath")

            val possibleDirs = listOf(androidSharePath)

            var shareDir: File? = null

            for (dirPath in possibleDirs) {
                val testDir = File(dirPath)
                try {
                    if (!testDir.exists()) testDir.mkdirs()
                    val testFile = File(testDir, "test_write_permission.tmp")
                    testFile.writeText("test")
                    testFile.delete()
                    shareDir = testDir
                    LogUtils.i(TAG, "使用目标目录: ${testDir.absolutePath}")
                    break
                } catch (e: Exception) {
                    LogUtils.w(TAG, "目录 $dirPath 不可用: ${e.message}")
                }
            }

            if (shareDir == null) {
                LogUtils.e(TAG, "无法找到可用的目标拷贝目录")
                // 发送错误通知
                context?.let {
                    NotificationUtils.showErrorNotification(
                        it,
                        "无法找到可用的目标拷贝目录"
                    )
                }
                return false
            }

            var successCount = 0
            for (dcItem in validPackages) {
                val sourceFileName = extractFileNameFromPath(dcItem.path)
                val sourceFile = findFileInUsbDir(usbDir, sourceFileName)
                if (sourceFile != null && sourceFile.exists()) {
                    try {
                        val targetFile = File(shareDir, sourceFileName)
                        copyFile(sourceFile, targetFile)
                        LogUtils.d(TAG, "文件拷贝成功: ${sourceFile.name} -> ${targetFile.absolutePath}")
                        successCount++
                    } catch (e: Exception) {
                        LogUtils.e(TAG, "拷贝文件失败 ${sourceFile.name}: ${e.message}")
                    }
                } else {
                    LogUtils.e(TAG, "源文件不存在: $sourceFileName")
                }
            }

            successCount > 0
        } catch (e: Exception) {
            LogUtils.e(TAG, "拷贝升级文件失败: ${e.message}", e)
            // 发送错误通知
            context?.let {
                NotificationUtils.showErrorNotification(
                    it,
                    "拷贝升级文件异常: ${e.message}"
                )
            }
            false
        }
    }

    private fun copyFile(source: File, target: File) {
        val totalSize = source.length()
        var copiedSize = 0L
        var lastLogTime = System.currentTimeMillis()
        val logInterval = 2000L // 每2秒记录一次进度

        LogUtils.i(TAG, "开始拷贝文件: ${source.name} (${formatFileSize(totalSize)})")

        FileInputStream(source).use { input ->
            FileOutputStream(target).use { output ->
                val buffer = ByteArray(8192)
                var bytesRead: Int
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    output.write(buffer, 0, bytesRead)
                    copiedSize += bytesRead

                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastLogTime >= logInterval || copiedSize == totalSize) {
                        val progress = if (totalSize > 0) (copiedSize * 100 / totalSize) else 0
                        LogUtils.d(TAG, "拷贝进度: ${source.name} - ${progress}% (${formatFileSize(copiedSize)}/${formatFileSize(totalSize)})")
                        lastLogTime = currentTime
                    }
                }
                output.flush()
            }
        }

        LogUtils.i(TAG, "文件拷贝完成: ${source.name} -> ${target.absolutePath}")
    }

    private fun formatFileSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }

    private fun publishTaskToDds(taskJson: String) {
        val success = ddsService?.publishUpgradeTaskInfo(taskJson) ?: false
        if (success) {
            updateTaskStatus(TaskStatus.PUBLISHED)
            context?.let {
                NotificationUtils.showCompletionNotification(
                    it,
                    "升级任务发布"
                )
            }
        } else {
            updateTaskStatus(TaskStatus.FAILED)
        }
    }

    private fun updateTaskStatus(status: TaskStatus) {
        currentTask?.let {
            currentTask = it.copy(status = status)
            LogUtils.d(TAG, "任务状态更新: ${it.taskId} -> $status")
        }
    }

    fun cancelCurrentTask() {
        currentTask?.let {
            updateTaskStatus(TaskStatus.CANCELLED)
            LogUtils.i(TAG, "已取消任务: ${it.taskId}")
            currentTask = null
        }
    }

    private fun generateTaskId(): String {
        return "UPGRADE_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    private fun extractFileNameFromPath(path: String): String {
        return if (path.contains("/")) {
            // 如果路径中包含 /，提取最后的文件名部分
            path.substringAfterLast("/")
        } else {
            // 如果路径中不包含 /，直接返回原字符串
            path
        }
    }

    private fun findFileInUsbDir(usbDir: File, fileName: String): File? {
        return try {
            val allFiles = getAllFilesRecursive(usbDir)
            allFiles.find { file ->
                file.name.equals(fileName, ignoreCase = true) ||
                        file.name.contains(fileName, ignoreCase = true)
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "查找文件失败: $fileName - ${e.message}")
            null
        }
    }

    private fun getAllFilesRecursive(dir: File): List<File> {
        val files = mutableListOf<File>()
        try {
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    files.add(file)
                } else if (file.isDirectory) {
                    files.addAll(getAllFilesRecursive(file))
                }
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "扫描目录失败: ${dir.absolutePath} - ${e.message}")
        }
        return files
    }

    /**
     * 创建发送给DDS的任务JSON（移除共享路径字段）
     */
    private fun createDdsTaskJson(upgradeTaskInfo: UpgradeTaskInfo): String {
        return try {
            // 创建一个副本，移除共享路径字段
            val ddsTaskInfo = UpgradeTaskInfo(
                edition = upgradeTaskInfo.edition,
                androidSharedPath = "", // 移除android_shared_path
                yoctoSharedPath = "",   // 移除yocto_shared_path
                dpPackage = upgradeTaskInfo.dpPackage
            )

            val taskJson = gson
                .newBuilder()
                .setPrettyPrinting()
                .create()
                .toJson(ddsTaskInfo)

            LogUtils.d(TAG, "生成DDS任务JSON，已移除共享路径字段")
            taskJson
        } catch (e: Exception) {
            LogUtils.e(TAG, "创建DDS任务JSON失败: ${e.message}", e)
            // 如果失败，返回原始JSON
            gson.newBuilder().setPrettyPrinting().create().toJson(upgradeTaskInfo)
        }
    }
}

package com.seres.background.ota.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.seres.background.ota.analyzer.UpgradeTaskAnalyzer
import com.seres.background.ota.manager.UpgradeTaskManager
import com.seres.background.ota.publisher.DdsService
import com.seres.background.ota.utils.LogUtils
import com.seres.background.ota.utils.NotificationUtils
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * USB检测服务
 * 监听USB设备插拔，检测upgrade_task_info.json文件
 */
class UsbDetectionService : Service(), DdsService.InventoryInfoCallback {
    private var threadPool : ExecutorService? = null
    private var usbDetectionReceiver: UsbDetectionReceiver? = null
    private var upgradeTaskAnalyzer: UpgradeTaskAnalyzer? = null
    private var upgradeTaskManager: UpgradeTaskManager? = null
    private var ddsService: DdsService? = null
    private var serviceStarted = false
    // 防止重复初始化的标志
    @Volatile
    private var componentsInitialized = false
    
    companion object {
        // 配置文件名
        const val TAG = "UsbDetectionService"
        const val UPGRADE_TASK_FILE = "upgrade_task_info.json"
    }
    
    override fun onCreate() {
        super.onCreate()
        LogUtils.i(TAG, "UsbDetectionService onCreate")
        // 创建前台服务通知
        val notification = NotificationUtils.initUsbDetectionNotification(this)
        startForeground(NotificationUtils.USB_DETECTION_NOTIFICATION_ID, notification)
        usbDetectionReceiver = UsbDetectionReceiver.getInstance(this)
        usbDetectionReceiver?.startDetection(object : UsbDetectionReceiver.DetectionCallback {
            override fun onDeviceAttached(usbPath: File) {
                LogUtils.i(TAG, "USB 插入: ${usbPath.absolutePath}")
                initializeComponents()
                handleUsbMounted(usbPath.absolutePath)
            }

            override fun onDeviceDetached(usbPath: File?) {
                LogUtils.i(TAG, "USB 移除: ${usbPath?.absolutePath}")
                usbPath?.let { handleUsbUnmounted(it.absolutePath) }
            }
        })
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.i(TAG, "UsbDetectionService onStartCommand: ${intent?.action} - flags: $flags, startId: $startId")
        return START_STICKY // 服务被杀死后自动重启
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        if(serviceStarted){
            LogUtils.e(TAG, "已经初始化")
            return
        }
        synchronized(this) {
            try {
                serviceStarted = true
                // 初始化升级任务管理器
                threadPool = Executors.newCachedThreadPool()
                upgradeTaskManager = UpgradeTaskManager().apply {
                    setContext(this@UsbDetectionService)
                }
                upgradeTaskAnalyzer = UpgradeTaskAnalyzer().apply {
                    setContext(this@UsbDetectionService)
                }
                // 初始化DDS服务
                ddsService = DdsService().apply {
                    setContext(this@UsbDetectionService)
                    setInventoryInfoCallback(this@UsbDetectionService)
                    if (!initialize()) {
                        LogUtils.e(TAG, "DDS服务初始化失败")
                    }
                }
                // 设置升级任务管理器的DDS服务
                upgradeTaskManager?.setDdsService(ddsService!!)
                componentsInitialized = true
                LogUtils.i(TAG, "组件初始化完成")
            } catch (e: Exception) {
                LogUtils.e(TAG, "组件初始化失败: ${e.message}")
            }
        }
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        LogUtils.i(TAG, "UsbDetectionService onDestroy - 尝试重启服务")
        usbDetectionReceiver?.stopDetection()
        cleanup()
    }
    
    /**
     * 处理USB设备挂载
     */
    private fun handleUsbMounted(path: String) {
        threadPool?.submit {
            try {
                val usbDir = File("$path/UpgradeFiles")
                if (usbDir.exists() && usbDir.isDirectory) {
                    // 检查是否包含upgrade_task_info.json文件
                    val upgradeTaskFile = File(usbDir, UPGRADE_TASK_FILE)
                    if (upgradeTaskFile.exists()) {
                        LogUtils.i(TAG, "发现upgrade_task_info.json文件: ${upgradeTaskFile.absolutePath}")
//                        val notification = NotificationUtils.initUsbDetectionNotification(this@UsbDetectionService)
//                        startForeground(NotificationUtils.USB_DETECTION_NOTIFICATION_ID, notification)
                        NotificationUtils.updateUsbDetectionNotification(
                            this@UsbDetectionService,
                            "发现升级任务配置文件"
                        )
                        // 分析升级任务
                        upgradeTaskAnalyzer?.analyzeUpgradeTask(usbDir, upgradeTaskManager!!)
                    } else {
                        LogUtils.d(TAG, "USB设备中未发现upgrade_task_info.json文件: $path")
                    }
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "处理USB挂载事件失败: ${e.message}")
                NotificationUtils.updateUsbDetectionNotification(
                    this@UsbDetectionService,
                    "处理USB设备时出错"
                )
            }
        }
    }
    
    /**
     * 处理USB设备卸载
     */
    private fun handleUsbUnmounted(path: String) {
        LogUtils.i(TAG, "USB设备已卸载: $path")
        NotificationUtils.updateUsbDetectionNotification(this, "USB设备已移除")
        // 取消相关的升级任务
        upgradeTaskManager?.cancelCurrentTask()
    }

    /**
     * DDS资产信息回调实现
     */
    override fun onInventoryInfoReceived(inventoryInfoStatus: String) {
        try {
            LogUtils.d(TAG, "收到资产信息通知: $inventoryInfoStatus")
            val isMatched = upgradeTaskAnalyzer?.parseInventoryInfoIsMatched(inventoryInfoStatus)
            val res = isMatched?.let {
                if(it){
                    LogUtils.i(TAG, "资产信息匹配成功")
                    this.let { contextIt ->
                        NotificationUtils.showCompletionNotification(
                            contextIt,
                            "资产信息比对完成，确认开始升级"
                        )
                    }
                }else{
                    LogUtils.e(TAG, "资产信息不匹配，无法升级")
                    this.let { contextIt ->
                        NotificationUtils.showCompletionNotification(
                            contextIt,
                            "资产信息比对完成，资产信息不匹配，无法升级"
                        )
                    }
                }
                ddsService?.publishUpgradeTaskMatch(it)
            }
            if(res != true){
                LogUtils.e(TAG, "发送UpgradeTaskMatch失败")
            }else{
                LogUtils.i(TAG, "发布UpgradeTaskMatch,res : $res")
                cleanup()
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理资产信息时出错: ${e.message}")
        }
    }

    /**
     * 清理资源 (从BackgroundUpgradeService合并)
     */
    private fun cleanup() {
        synchronized(this) {
            try {
                upgradeTaskManager = null
                upgradeTaskAnalyzer = null
                // 清理DDS服务
                ddsService?.cleanup()
                ddsService = null
                // 关闭线程池
                threadPool?.shutdown()
                // 重置初始化标志
                serviceStarted = false
                componentsInitialized = false
                LogUtils.i(TAG, "资源清理完成")
            } catch (e: Exception) {
                LogUtils.e(TAG, "清理资源时出错: ${e.message}")
            }
        }
    }

}

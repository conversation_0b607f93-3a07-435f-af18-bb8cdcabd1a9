plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace 'com.seres.background.ota'
    compileSdk 34

    defaultConfig {
        applicationId "com.seres.background.ota"
        minSdk 31
        targetSdk 34
        versionCode 1
        versionName "1.2.8"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            // enableV3Signing true
            storeFile file('../keystore/platform.jks')
            storePassword "seres@2025"
            keyAlias "seres"
            keyPassword "seres@2025"
        }
        release {
            storeFile file('../keystore/platform.jks')
            storePassword "seres@2025"
            keyAlias "seres"
            keyPassword "seres@2025"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.debug
            minifyEnabled false
        }
    }

    applicationVariants.all { variant ->
        if (variant.buildType.name == "release") {
            variant.outputs.all { output ->
                outputFileName = "${rootProject.name}.apk"
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        aidl false
    }

    lint {
        abortOnError false
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.8.0"
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation files("libs/nativelib-release.aar")
//    testImplementation libs.junit
//    androidTestImplementation libs.androidx.junit
//    androidTestImplementation libs.androidx.espresso.core
//    implementation project(":nativelib")
//    implementation 'com.seres.s2s:sdk-api:1.0.0-SNAPSHOT'
//    compileOnly files('libs/framework.jar')
}

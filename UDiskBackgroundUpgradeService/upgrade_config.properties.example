# Upgrade Task Manager Configuration File
# 升级任务管理器配置文件
#
# 此文件用于配置升级文件的拷贝目标路径
# This file is used to configure the target path for copying upgrade files
#
# 配置文件查找顺序 (Configuration file search order):
# 1. /data/local/tmp/upgrade_config.properties
# 2. /system/etc/upgrade_config.properties  
# 3. /vendor/etc/upgrade_config.properties
# 4. /mnt/vendor/upgrade_config.properties
#
# 使用方法 (Usage):
# 1. 将此文件复制到上述任一路径
# 2. 修改 copy_path 的值为您想要的目标路径
# 3. 重新启动升级任务或调用 updateCopyPath() 方法
#
# Copy this file to one of the above paths
# Modify the copy_path value to your desired target path
# Restart the upgrade task or call updateCopyPath() method

# 升级文件拷贝目标路径 (Target path for copying upgrade files)
copy_path=/mnt/vendor/yocto_data

# 其他可选配置示例 (Other optional configuration examples):
# copy_path=/data/upgrade_files
# copy_path=/mnt/sdcard/upgrade
# copy_path=/storage/emulated/0/upgrade

# 注意事项 (Notes):
# - 路径必须存在或程序有权限创建
# - 路径必须有写入权限
# - 建议使用绝对路径
# - Path must exist or program has permission to create
# - Path must have write permission  
# - Absolute path is recommended

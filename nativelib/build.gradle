plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    id "maven-publish"
}

/**
    cmakeConfigure/cmakeBuild/cmakeInstall 主要用于通过cmakeList直接构建.so库，例如linux环境下所需的 mylib.so
 */
// 定义 CMake 构建目录和安装目录
def cmakeBuildDir = "${buildDir}/cmake"
def cmakeInstallDir = "${buildDir}/install"

// 注册 CMake 配置任务
tasks.register('cmakeConfigure', Exec) {
    workingDir = file(cmakeBuildDir)
    commandLine 'cmake', '-G', 'Unix Makefiles', "-DCMAKE_INSTALL_PREFIX=${cmakeInstallDir}", "${projectDir}/src/main/cpp"
    // 确保构建目录存在
    doFirst {
        file(cmakeBuildDir).mkdirs()
    }
} 

// 注册 CMake 构建任务
tasks.register('cmakeBuild', Exec) {
    dependsOn 'cmakeConfigure'
    workingDir = file(cmakeBuildDir)
    commandLine 'make'
}

// 注册 CMake 安装任务
tasks.register('cmakeInstall', Exec) {
    dependsOn 'cmakeConfigure'
    workingDir = file(cmakeBuildDir)
    commandLine 'make', 'install'
}


tasks.register('copyLibFiles', Copy){
    doFirst {
        def folder = file("${projectDir}/src/main/jniLibs/x86")
        if (!(folder.exists() && folder.isDirectory() && folder.listFiles()?.length > 0)) {
            throw new GradleException("目标文件夹 ${folder} 不为空，请先清空文件夹。")
        }
    }
    from "${projectDir}/src/main/cpp/libs/x86"
    into "${projectDir}/src/main/jniLibs/x86"
    include '**/*.so'
}

tasks.register('cleanJniLibs',Delete) {
    delete "${projectDir}/src/main/jniLibs/x86"
}

tasks.register('kscom-linux_version') {
    dependsOn 'cleanJniLibs', 'copyLibFiles','cmakeInstall'
    doLast{
        println "The Linux version has been successfully built, and the relevant dynamic libraries are located in the Jni/x86_64 folder"
    }
}

/*
*
*     构建Android ARR包插件
*
*/

android {
    namespace 'com.seres.scom.nativelib'
    compileSdk 34
    ndkVersion "27.2.12479018"

    defaultConfig {
        minSdk 31

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
        externalNativeBuild {
            cmake {
                if (abiFilters.contains('arm64-v8a')) {
                    cppFlags "-DANDROID_AAR"
                }
            }
        }
        ndk {
            abiFilters 'arm64-v8a'
        }
        externalNativeBuild {
            cmake {
                cppFlags ""
                // 指定只编译ARM平台
                abiFilters 'armeabi-v7a', 'arm64-v8a'
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.8.0"
}

task sourcesJar(type: Jar) {
    if (project.hasProperty("kotlin")) {
        from android.sourceSets.main.java.getSrcDirs()
    } else if (project.hasProperty("android")) {
        from android.sourceSets.main.java.sourceFiles
    } else {
        println project
        from sourceSets.main.allSource
    }
    archiveClassifier = 'sources'
}

artifacts {
    archives sourcesJar
}

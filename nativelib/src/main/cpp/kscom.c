
#include "kscom.h"

const struct dds_cdrstream_allocator cdrstream_allocator = { dds_alloc, dds_realloc, dds_free };
typedef struct ddsi_sertype ddsi_sertype_t;
typedef struct ddsi_serdata ddsi_serdata_t;

typedef struct kscom_sertype {
    ddsi_sertype_t c_type;
    bool keyless;
    bool is_v2_by_default;

    bool v0_key_maxsize_bigger_16;
    bool v2_key_maxsize_bigger_16;

    // xtypes
    unsigned char *typeinfo_ser_data;
    uint32_t typeinfo_ser_sz;
    unsigned char *typemap_ser_data;
    uint32_t typemap_ser_sz;
    struct dds_cdrstream_desc cdrstream_desc;
} kscom_sertype_t;


typedef struct kscom_serdata {
    ddsi_serdata_t c_data;
    void *data;
    size_t data_size;     // size of the data, including 4 bytes for CDR encapsulation header
    void *key;
    size_t key_size;      // size of the key, including 4 bytes for CDR encapsulation header
    bool data_is_key;
    bool is_v2;
} kscom_serdata_t;


/**
 * participant impl
 */

dds_entity_t kscom_create_participant(const dds_domainid_t domain, const dds_qos_t *qos, const dds_listener_t *listener)
{
    return dds_create_participant(domain, qos, listener);
}


/**
 * publisher impl
 */
dds_entity_t kscom_create_publisher(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener)
{
    return dds_create_publisher(participant, qos, listener);
}


dds_return_t kscom_suspend(dds_entity_t publisher)
{
    return dds_suspend(publisher);
}


dds_return_t kscom_resume(dds_entity_t publisher)
{
    return dds_resume(publisher);
}


dds_return_t kscom_wait_for_acks(dds_entity_t publisher_or_writer, dds_duration_t timeout)
{
    return dds_wait_for_acks(publisher_or_writer, timeout);
}


/**
 * subscriber impl
 */
dds_entity_t kscom_create_subscriber(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener)
{
    return dds_create_subscriber(participant, qos, listener);
}


dds_return_t kscom_notify_readers(dds_entity_t subscriber)
{
    return dds_notify_readers(subscriber);
}


/**
 * reader impl
 */
dds_entity_t kscom_create_reader(dds_entity_t participant_or_subscriber, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener)
{
    return dds_create_reader(participant_or_subscriber, topic, qos, listener);
}



dds_entity_t kscom_create_writer(dds_entity_t participant_or_publisher, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener)
{
    return dds_create_writer(participant_or_publisher, topic, qos, listener);
}


/**
 * condition impl
 */
dds_return_t kscom_get_mask(dds_entity_t condition, uint32_t *mask){
    return dds_get_mask(condition,mask);
}

dds_return_t kscom_triggered(dds_entity_t entity){
    return dds_triggered(entity);
}

dds_entity_t kscom_create_readcondition(dds_entity_t reader, uint32_t mask){
    return dds_create_readcondition(reader,mask);
}

dds_entity_t kscom_create_querycondition(dds_entity_t reader, uint32_t mask, dds_querycondition_filter_fn filter){
    return dds_create_querycondition(reader,mask,filter);
}

dds_entity_t kscom_create_guardcondition(dds_entity_t owner){
    return dds_create_guardcondition(owner);
}

dds_return_t kscom_set_guardcondition(dds_entity_t guardcond, bool triggered){
    return dds_set_guardcondition(guardcond, triggered);
}

dds_return_t kscom_read_guardcondition(dds_entity_t guardcond, bool *triggered){
    return dds_read_guardcondition(guardcond,triggered);
}

dds_return_t kscom_take_guardcondition(dds_entity_t guardcond, bool *triggered){
    return dds_take_guardcondition(guardcond, triggered);
}

/**
 * waitset
 */
dds_entity_t kscom_create_waitset(dds_entity_t owner){
    return dds_create_waitset(owner);
}

dds_return_t kscom_waitset_attach(dds_entity_t waitset, dds_entity_t entity, dds_attach_t x){
    return dds_waitset_attach(waitset, entity, x);
}

dds_return_t kscom_waitset_detach(dds_entity_t waitset, dds_entity_t entity){
    return dds_waitset_detach(waitset, entity);
}

dds_return_t kscom_waitset_wait(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_duration_t reltimeout){
    return dds_waitset_wait(waitset, xs, nxs, reltimeout);
}

dds_return_t kscom_waitset_wait_until(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_time_t abstimeout){
    return dds_waitset_wait_until(waitset,xs,nxs,abstimeout);
}

dds_return_t kscom_waitset_set_trigger(dds_entity_t waitset, bool trigger){
    return dds_waitset_set_trigger(waitset, trigger);
}

/**
 * serdata impl
 */

static kscom_serdata_t *kscom_serdata_new (const struct ddsi_sertype *type, enum ddsi_serdata_kind kind, size_t data_size)
{
    kscom_serdata_t *new = dds_alloc (sizeof (kscom_serdata_t));
    ddsi_serdata_init ((ddsi_serdata_t *)new, type, kind);
    new->data = dds_alloc (data_size);
    new->data_size = data_size;
    new->key = NULL;
    new->key_size = 0;
    new->data_is_key = false;
    new->is_v2 = ((kscom_sertype_t *)type)->is_v2_by_default;
    return new;
}

static inline const kscom_sertype_t *csertype (const kscom_serdata_t *this)
{
    return (const kscom_sertype_t *) (this->c_data.type);
}

static inline const kscom_serdata_t *cserdata (const ddsi_serdata_t *this)
{
    return (const kscom_serdata_t *) (this);
}

static inline kscom_sertype_t *sertype (kscom_serdata_t *this)
{
return (kscom_sertype_t *) (this->c_data.type);
}

static inline kscom_serdata_t *serdata (ddsi_serdata_t *this)
{
return (kscom_serdata_t *) (this);
}

static void serdata_sanity_check (const kscom_serdata_t *d, bool check_key)
{
    //todo  data check
    // assert (d->data != NULL);
    // assert (d->data_size != 0);
    (void)check_key;
    //未序列化key todo
    // if (check_key)
    // {
    //   assert (d->key != NULL);
    //   assert (d->key_size >= 20);
    // }
}

static bool serdata_eqkey (const struct ddsi_serdata *a, const struct ddsi_serdata *b)
{
    const kscom_serdata_t *apy = cserdata(a), *bpy = cserdata(b);
    if (csertype(apy)->keyless ^ csertype(bpy)->keyless)
        return false;
    if (csertype(apy)->keyless && csertype(bpy)->keyless)
        return true;
    assert (cserdata(a)->key != NULL);
    assert (cserdata(b)->key != NULL);
    if (cserdata(a)->key_size != cserdata(b)->key_size) return false;
    return 0 == memcmp (cserdata(a)->key, cserdata(b)->key, cserdata(a)->key_size);
}


static uint32_t serdata_size (const struct ddsi_serdata *dcmn)
{
    // assert (cserdata(dcmn)->key != NULL);
    assert (cserdata(dcmn)->data != NULL);
    if (dcmn->kind == SDK_KEY)
        return (uint32_t)cserdata(dcmn)->key_size;
    else
        return (uint32_t)cserdata(dcmn)->data_size;
}


static bool kscom_serdata_populate_key (kscom_serdata_t *this)
{
    const uint32_t xcdr_version = this->is_v2 ? DDSI_RTPS_CDR_ENC_VERSION_2 : DDSI_RTPS_CDR_ENC_VERSION_1;
    void *cdr_hdr = this->data;
    void *cdr_data = (char *)this->data + 4;

    uint32_t act_size;
    if (!dds_stream_normalize (cdr_data, (uint32_t)this->data_size - 4, false, xcdr_version, &csertype(this)->cdrstream_desc, (this->c_data.kind == SDK_KEY), &act_size))
        return false;

    dds_ostream_t os;
    dds_ostream_init (&os, &cdrstream_allocator, 0, xcdr_version);
    dds_istream_t is;
    dds_istream_init (&is, (uint32_t)this->data_size - 4, cdr_data, xcdr_version);

    bool extract_result;
    if (this->c_data.kind == SDK_KEY)
    {
        dds_stream_extract_key_from_key (&is, &os, DDS_CDR_KEY_SERIALIZATION_SAMPLE, &cdrstream_allocator, &csertype(this)->cdrstream_desc);
        extract_result = true;
    }
    else
    {
        extract_result = dds_stream_extract_key_from_data (&is, &os, &cdrstream_allocator, &csertype(this)->cdrstream_desc);
    }
    if (extract_result)
    {
        this->key_size = os.m_index + 4;
        this->key = dds_alloc (this->key_size);
        memcpy (this->key, cdr_hdr, 4);
        memcpy ((char *)this->key + 4, os.m_buffer, os.m_index);
    }
    dds_ostream_fini (&os, &cdrstream_allocator);
    return extract_result;
}

static uint32_t hash_value (void *data, const size_t sz)
{
    if (sz == 0)
        return 0;

    unsigned char buf[16];
    ddsrt_md5_state_t md5st;
    ddsrt_md5_init (&md5st);
    ddsrt_md5_append (&md5st, (ddsrt_md5_byte_t *)data, (uint32_t)sz);
    ddsrt_md5_finish (&md5st, (ddsrt_md5_byte_t *)buf);
    return *(uint32_t *)buf;
}

static void kscom_serdata_populate_hash (kscom_serdata_t *this)
{
    ddsi_serdata_t *sd = (ddsi_serdata_t *)this;

    // set initial hash to that of type
    sd->hash = sd->type->serdata_basehash;

    // xor type hash with hash of key
    const uint32_t key_hash = hash_value (this->key, this->key_size);
    sd->hash ^= key_hash;
}


static ddsi_serdata_t *serdata_from_common (kscom_serdata_t *d, enum ddsi_serdata_kind kind)
{
    // 目前没有序列化key  待序列化key后实现
    // d->is_v2 = ((char *)d->data)[1] > 1;
    // if (!kscom_serdata_populate_key (d))
    // {
    //   ddsi_serdata_unref ((ddsi_serdata_t *)d);
    //   return NULL;
    // }
    // kscom_serdata_populate_hash (d);
    // serdata_sanity_check (d, true);
    return (ddsi_serdata_t *)d;
}



static ddsi_serdata_t *serdata_from_ser (const struct ddsi_sertype *type, enum ddsi_serdata_kind kind, const struct ddsi_rdata *fragchain, size_t size)
{
    kscom_serdata_t *d = kscom_serdata_new (type, kind, size);

    uint32_t off = 0;
    assert (fragchain->min == 0);
    assert (fragchain->maxp1 >= off);    //CDR header must be in first fragment

    unsigned char *cursor = d->data;
    while (fragchain)
    {
        if (fragchain->maxp1 > off)
        {
            //only copy if this fragment adds data
            const unsigned char *payload =
                    DDSI_RMSG_PAYLOADOFF (fragchain->rmsg, DDSI_RDATA_PAYLOAD_OFF (fragchain));
            const unsigned char *src = payload + off - fragchain->min;
            size_t n_bytes = fragchain->maxp1 - off;
            memcpy (cursor, src, n_bytes);
            cursor += n_bytes;
            off = fragchain->maxp1;
            assert (off <= size);
        }
        fragchain = fragchain->nextfrag;
    }
    return serdata_from_common (d, kind);
}


static ddsi_serdata_t *serdata_from_ser_iov (const struct ddsi_sertype *type, enum ddsi_serdata_kind kind, ddsrt_msg_iovlen_t niov, const ddsrt_iovec_t *iov, size_t size)
{
    kscom_serdata_t *d = kscom_serdata_new (type, kind, size);

    size_t off = 0;
    unsigned char *cursor = d->data;
    for (ddsrt_msg_iovlen_t i = 0; i < niov && off < size; i++)
    {
        size_t n_bytes = iov[i].iov_len;
        if (n_bytes + off > size) n_bytes = size - off;
        memcpy (cursor, iov[i].iov_base, n_bytes);
        cursor += n_bytes;
        off += n_bytes;
    }
    return serdata_from_common (d, kind);
}


static ddsi_serdata_t *serdata_from_keyhash (const struct ddsi_sertype *topic, const struct ddsi_keyhash *keyhash)
{
    (void)keyhash;
    (void)topic;
    //replace with (if key_size_max <= 16) then populate the data class with the key hash (key_read)
    // TODO
    assert (0);
    return NULL;
}


static ddsi_serdata_t *serdata_from_sample (const ddsi_sertype_t *type, enum ddsi_serdata_kind kind, const void *sample)
{
    kscom_sample_container_t *container = (kscom_sample_container_t *)sample;
    kscom_serdata_t *d = kscom_serdata_new (type, kind, container->usample_size);
    memcpy ((char *)d->data, container->usample, container->usample_size);
    return serdata_from_common (d, kind);
}


static void serdata_to_ser (const ddsi_serdata_t *dcmn, size_t off, size_t sz, void *buf)
{
    serdata_sanity_check (cserdata(dcmn), true);
    if (dcmn->kind == SDK_KEY)
        memcpy (buf, (char *)cserdata(dcmn)->key + off, sz);
    else
        memcpy (buf, (char *)cserdata(dcmn)->data + off, sz);
}


static ddsi_serdata_t *serdata_to_ser_ref (const struct ddsi_serdata *dcmn, size_t off, size_t sz, ddsrt_iovec_t *ref)
{
    serdata_sanity_check (cserdata(dcmn), true);
    if (dcmn->kind == SDK_KEY)
    {
        ref->iov_base = (char *)cserdata(dcmn)->key + off;
        ref->iov_len = (ddsrt_iov_len_t)sz;
    }
    else
    {
        ref->iov_base = (char *)cserdata(dcmn)->data + off;
        ref->iov_len = (ddsrt_iov_len_t)sz;
    }
    return ddsi_serdata_ref (dcmn);
}


static void serdata_to_ser_unref (struct ddsi_serdata *dcmn, const ddsrt_iovec_t *ref)
{
    (void)ref;    // unused
    ddsi_serdata_unref (dcmn);
}


static bool serdata_to_sample (const ddsi_serdata_t *dcmn, void *sample, void **bufptr, void *buflim)
{
    (void)bufptr;
    (void)buflim;
    kscom_sample_container_t *container = (kscom_sample_container_t *)sample;

    serdata_sanity_check (cserdata(dcmn), true);
    assert (container->usample == NULL);

    container->usample = dds_alloc (cserdata(dcmn)->data_size);
    memcpy (container->usample, cserdata(dcmn)->data, cserdata(dcmn)->data_size);
    container->usample_size = cserdata(dcmn)->data_size;
    return true;
}


static ddsi_serdata_t *serdata_to_typeless (const ddsi_serdata_t *dcmn)
{
    serdata_sanity_check (cserdata(dcmn), true);

    if (dcmn->kind == SDK_KEY)
        return ddsi_serdata_ref (dcmn);

    const kscom_serdata_t *d = cserdata(dcmn);
    kscom_serdata_t *d_tl = dds_alloc (sizeof (kscom_serdata_t));
    assert (d_tl);
    ddsi_serdata_init ((ddsi_serdata_t *)d_tl, dcmn->type, SDK_KEY);
    d_tl->data = ddsrt_memdup (d->key, d->key_size);
    d_tl->key = d_tl->data;
    d_tl->data_size = d->key_size;
    d_tl->key_size = d->key_size;
    d_tl->data_is_key = true;
    d_tl->is_v2 = false;
    d_tl->c_data.hash = d->c_data.hash;
    return (struct ddsi_serdata *)d_tl;
}


static bool serdata_typeless_to_sample (const struct ddsi_sertype *type, const struct ddsi_serdata *dcmn, void *sample, void **buf, void *buflim)
{
    kscom_sample_container_t *container = (kscom_sample_container_t *)sample;
    (void)type;
    (void)buf;
    (void)buflim;

    serdata_sanity_check (cserdata(dcmn), true);
    assert (container->usample == NULL);

    container->usample = dds_alloc (cserdata(dcmn)->data_size);
    container->usample_size = cserdata(dcmn)->data_size;
    memcpy (container->usample, cserdata(dcmn)->data, container->usample_size);
    return true;
}


static void serdata_free (struct ddsi_serdata *dcmn)
{
    serdata_sanity_check (cserdata(dcmn), false);
    if (cserdata(dcmn)->data_size == 0){
        return;
    } // todo
    dds_free (serdata (dcmn)->data);
    if (!serdata (dcmn)->data_is_key)
        dds_free (serdata (dcmn)->key);
    dds_free (dcmn);
}


static size_t serdata_print (const struct ddsi_sertype *tpcmn, const struct ddsi_serdata *dcmn, char *buf, size_t bufsize)
{
    (void)tpcmn;
    (void)dcmn;
    (void)buf;
    (void)bufsize;
    return 0;
}


static void serdata_get_keyhash (const ddsi_serdata_t *d, struct ddsi_keyhash *buf, bool force_md5)
{
    assert (cserdata(d)->data != NULL);
    assert (cserdata(d)->data_size != 0);
    assert (d->type != NULL);

    if (csertype (cserdata(d))->keyless)
    {
        memset (buf->value, 0, 16);
        return;
    }

    const void *le_key = ((char *)cserdata(d)->key) + 4;
    size_t le_keysz = cserdata(d)->key_size - 4;
    bool is_v2 = cserdata(d)->is_v2;
    bool v0_key_maxsize_bigger_16 = csertype (cserdata(d))->v0_key_maxsize_bigger_16;
    bool v2_key_maxsize_bigger_16 = csertype (cserdata(d))->v2_key_maxsize_bigger_16;

    assert (le_key != NULL);
    assert (le_keysz > 0);

    dds_istream_t is;
    dds_istream_init (&is, le_keysz, le_key, DDSI_RTPS_CDR_ENC_VERSION_2);
    dds_ostreamBE_t os;
    dds_ostreamBE_init (&os, &cdrstream_allocator, 16, is_v2 ? DDSI_RTPS_CDR_ENC_VERSION_2 : DDSI_RTPS_CDR_ENC_VERSION_1);
    dds_stream_extract_keyBE_from_key (&is, &os, DDS_CDR_KEY_SERIALIZATION_KEYHASH, &cdrstream_allocator, &csertype (cserdata(d))->cdrstream_desc);
    assert (is.m_index == le_keysz);

    void *be_key = os.x.m_buffer;
    size_t be_keysz = os.x.m_index;

    if (be_keysz < 16)
        memset ((char *)be_key + be_keysz, 0, 16 - be_keysz);
    if (force_md5 || (is_v2 && v2_key_maxsize_bigger_16) || (!is_v2 && v0_key_maxsize_bigger_16))
    {
        ddsrt_md5_state_t md5st;
        ddsrt_md5_init (&md5st);
        ddsrt_md5_append (&md5st, be_key, be_keysz > 16 ? (uint32_t)be_keysz : 16);
        ddsrt_md5_finish (&md5st, buf->value);
    }
    else
    {
        assert (be_keysz <= 16);
        memset (buf->value, 0, 16);
        memcpy (buf->value, be_key, be_keysz);
    }
    dds_ostreamBE_fini (&os, &cdrstream_allocator);
}


const struct ddsi_serdata_ops kscom_serdata_ops = {
        &serdata_eqkey,
        &serdata_size,
        &serdata_from_ser,
        &serdata_from_ser_iov,
        &serdata_from_keyhash,
        &serdata_from_sample,
        &serdata_to_ser,
        &serdata_to_ser_ref,
        &serdata_to_ser_unref,
        &serdata_to_sample,
        &serdata_to_typeless,
        &serdata_typeless_to_sample,
        &serdata_free,
        &serdata_print,
        &serdata_get_keyhash
};


/**
 * sertype impl
 */

#ifdef DDS_HAS_TYPELIB
static dds_return_t init_cdrstream_descriptor (kscom_sertype_t *sertype)
{
  dds_return_t ret = DDS_RETCODE_OK;

  ddsi_typeinfo_t *type_info = ddsi_typeinfo_deser (sertype->typeinfo_ser_data, sertype->typeinfo_ser_sz);
  if (type_info == NULL) {
    ret = DDS_RETCODE_ERROR;
    goto err_typeinfo;
  }

  const ddsi_typeid_t *type_id = ddsi_typeinfo_complete_typeid (type_info);
  if (type_id == NULL) {
    ret = DDS_RETCODE_ERROR;
    goto err;
  }

  struct ddsi_domaingv *const gv = ddsrt_atomic_ldvoidp (&sertype->c_type.gv);
  if (gv == NULL) {
    ret = DDS_RETCODE_ERROR;
    goto err;
  }

  const struct ddsi_type *ddsi_type = ddsi_type_lookup (gv, type_id);
  if (ddsi_type == NULL) {
    ret = DDS_RETCODE_ERROR;
    goto err;
  }

  dds_topic_descriptor_t desc;
  if ((ret = ddsi_topic_descriptor_from_type (gv, &desc, ddsi_type)) != DDS_RETCODE_OK)
    goto err;

  dds_cdrstream_desc_init (&sertype->cdrstream_desc, &cdrstream_allocator, desc.m_size, desc.m_align, desc.m_flagset, desc.m_ops, desc.m_keys, desc.m_nkeys);
  sertype->v0_key_maxsize_bigger_16 = !(sertype->cdrstream_desc.flagset & DDS_TOPIC_FIXED_KEY);
  sertype->v2_key_maxsize_bigger_16 = !(sertype->cdrstream_desc.flagset & DDS_TOPIC_FIXED_KEY_XCDR2);
  ddsi_topic_descriptor_fini (&desc);

 err:
  ddsi_typeinfo_free (type_info);
 err_typeinfo:
  return ret;
}
#endif

static bool sertype_equal (const ddsi_sertype_t *acmn, const ddsi_sertype_t *bcmn)
{
    const kscom_sertype_t *A = (const kscom_sertype_t *)acmn;
    const kscom_sertype_t *B = (const kscom_sertype_t *)bcmn;

    if (A == B)
        return true;

    if (A->keyless != B->keyless)
        return false;

    if (A->is_v2_by_default != B->is_v2_by_default)
        return false;

    //todo xtypes identity

    return true;
}


static uint32_t sertype_hash (const struct ddsi_sertype *tpcmn)
{
    (void)tpcmn;
    return 0x0u;
}


static void sertype_free (struct ddsi_sertype *tpcmn)
{
    kscom_sertype_t *this = (kscom_sertype_t *)tpcmn;
    if (this->typeinfo_ser_sz)
        dds_free (this->typeinfo_ser_data);
    if (this->typemap_ser_sz)
        dds_free (this->typemap_ser_data);
    dds_cdrstream_desc_fini (&this->cdrstream_desc, &cdrstream_allocator);

    ddsi_sertype_fini (tpcmn);
    dds_free (this);
}

static void sertype_zero_samples (const struct ddsi_sertype *sertype_common, void *samples, size_t count)
{
    (void)sertype_common;
    memset (samples, 0, sizeof (kscom_sample_container_t) *count);
}

static void sertype_realloc_samples (void **ptrs, const struct ddsi_sertype *sertype_common, void *old, size_t oldcount, size_t count)
{
    (void)sertype_common;
    char *new = (oldcount == count) ? old : dds_realloc (old, sizeof (kscom_sample_container_t) *count);
    if (new && count > oldcount)
        memset (new + sizeof (kscom_sample_container_t) *oldcount, 0, sizeof (kscom_sample_container_t) * (count - oldcount));

    for (size_t i = 0; i < count; i++)
    {
        void *ptr = (char *)new + i *sizeof (kscom_sample_container_t);
        ptrs[i] = ptr;
    }
}


static void sertype_free_samples (const struct ddsi_sertype *sertype_common, void **ptrs, size_t count, dds_free_op_t op)
{
    (void)sertype_common;
    if (count > 0)
    {
        if (op & DDS_FREE_CONTENTS_BIT)
        {
            if (((kscom_sample_container_t *)ptrs[0])->usample != NULL)
                dds_free (((kscom_sample_container_t *)ptrs[0])->usample);
        }

        if (op & DDS_FREE_ALL_BIT)
        {
            dds_free (ptrs[0]);
        }
    }
}

#ifdef DDS_HAS_TYPELIB
static ddsi_typeid_t *sertype_typeid (const struct ddsi_sertype *tpcmn, ddsi_typeid_kind_t kind)
{
  assert (tpcmn);
  assert (kind == DDSI_TYPEID_KIND_MINIMAL || kind == DDSI_TYPEID_KIND_COMPLETE);

  kscom_sertype_t *type = (kscom_sertype_t *)tpcmn;
  ddsi_typeinfo_t *type_info = ddsi_typeinfo_deser (type->typeinfo_ser_data, type->typeinfo_ser_sz);
  if (type_info == NULL)
    return NULL;
  ddsi_typeid_t *type_id = ddsi_typeinfo_typeid (type_info, kind);
  ddsi_typeinfo_fini (type_info);
  ddsrt_free (type_info);
  return type_id;
}


static ddsi_typemap_t *sertype_typemap (const struct ddsi_sertype *tpcmn)
{
  assert (tpcmn);
  kscom_sertype_t *type = (kscom_sertype_t *)tpcmn;
  return ddsi_typemap_deser (type->typemap_ser_data, type->typemap_ser_sz);
}


static ddsi_typeinfo_t *sertype_typeinfo (const struct ddsi_sertype *tpcmn)
{
  assert (tpcmn);
  kscom_sertype_t *type = (kscom_sertype_t *)tpcmn;
  return ddsi_typeinfo_deser (type->typeinfo_ser_data, type->typeinfo_ser_sz);
}

#endif

static struct ddsi_sertype *sertype_derive_sertype (const struct ddsi_sertype *base_sertype, dds_data_representation_id_t repr, dds_type_consistency_enforcement_qospolicy_t tceqos)
{
    // The python sertype can handle all types by itself, no derives needed
    DDSRT_UNUSED_ARG (repr);
    DDSRT_UNUSED_ARG (tceqos);
    return (struct ddsi_sertype *)base_sertype;
}

const struct ddsi_sertype_ops kscom_sertype_ops = {
        .version = ddsi_sertype_v0,
        .arg = NULL,
        .equal = sertype_equal,
        .hash = sertype_hash,
        .free = sertype_free,
        .zero_samples = sertype_zero_samples,
        .realloc_samples = sertype_realloc_samples,
        .free_samples = sertype_free_samples,
#ifdef DDS_HAS_TYPELIB
        .type_id = sertype_typeid,
  .type_map = sertype_typemap,
  .type_info = sertype_typeinfo,
#else
        .type_id = 0,
        .type_map = 0,
        .type_info = 0,
#endif
        .derive_sertype = sertype_derive_sertype
};


static kscom_sertype_t *kscom_sertype_new (idl_type_desc_t* desc)
{
    int pyversion_support = 1;
    uint8_t *xt_type_data = NULL;
    kscom_sertype_t *new = NULL;
    bool constructed = false;

    pyversion_support = desc->version_support;
    xt_type_data = desc->xt_type_data;

    const char *name = desc->type_name;
    if (name == NULL) goto err;

    bool keyless = desc->keyless;

    new = dds_alloc (sizeof (kscom_sertype_t));

    new->keyless = keyless;
    new->is_v2_by_default = pyversion_support == 2; // XCDRSupported.SupportsBasic = 1, SupportsV2 = 2

    //todo xtypes ser
    new->typemap_ser_data = NULL;
    new->typemap_ser_sz = 0;
    new->typeinfo_ser_data = NULL;
    new->typeinfo_ser_sz = 0;


    ddsi_sertype_init (&new->c_type, name, &kscom_sertype_ops, &kscom_serdata_ops, keyless);

    if (new->is_v2_by_default)
        new->c_type.allowed_data_representation = DDS_DATA_REPRESENTATION_FLAG_XCDR2;
    else
        new->c_type.allowed_data_representation = DDS_DATA_REPRESENTATION_FLAG_XCDR1 | DDS_DATA_REPRESENTATION_FLAG_XCDR2;

    constructed = true;

    err:
    if (new && !constructed)
    {
        dds_free (new);
        new = NULL;
    }

    return new;
}


/**
 * topic impl
 */
dds_entity_t kscom_create_topic(dds_entity_t participant, const char* topicname, idl_type_desc_t* desc, const dds_qos_t *qos, const dds_listener_t *listener)
{
    dds_entity_t sts;

    kscom_sertype_t *sertype = kscom_sertype_new (desc);

    if (sertype == NULL)
        return 0;

    sts = dds_create_topic_sertype (participant, topicname, (struct ddsi_sertype **) &sertype, qos, listener, NULL);

    if (sts < 0)
    {
        ddsi_sertype_unref ((struct ddsi_sertype *)sertype);
    }
    else
    {
#ifdef DDS_HAS_TYPELIB
        if (sertype->typeinfo_ser_data != NULL || sertype->typemap_ser_data != NULL)
    {
      dds_return_t ret = init_cdrstream_descriptor (sertype);
      if (ret != DDS_RETCODE_OK)
      {
        dds_delete (sts);
        sts = ret;
      }
    }
#endif
    }

    return sts;
}

static inline uint32_t check_number_of_samples (long long n)
{
    static const uint32_t max_samples = (UINT32_MAX / sizeof (dds_sample_info_t));

    if (n <= 0)
    {
        return 0u;
    }

    if (n > (long long)max_samples)
    {
        return 0u;
    }

    return (uint32_t)n;
}

static dds_return_t readtake_post (int32_t sts, dds_sample_info_t *info, kscom_sample_container_t *container, kscom_sample_container_t **rcontainer, samples* mysample)
{
    mysample->ncount = sts;
    if (sts != 0){

        mysample->kdata = malloc(sizeof(sample) * sts);

        for (int32_t i = 0; i < sts; ++i)
        {
            mysample->kdata[i].info = *info;
            mysample->kdata[i].container.usample = malloc(container[i].usample_size);
            memcpy(mysample->kdata[i].container.usample ,container[i].usample, container[i].usample_size);
            mysample->kdata[i].container.usample_size = container[i].usample_size;
            dds_free (container[i].usample);
        }
    }

    dds_free (info);
    dds_free (container);
    dds_free (rcontainer);
}

static bool readtake_pre (long long N, uint32_t *Nu32, dds_sample_info_t **info, kscom_sample_container_t **container, kscom_sample_container_t ***rcontainer)
{
    if (!(*Nu32 = check_number_of_samples (N)))
        return false;

    *info = dds_alloc (sizeof (**info) * *Nu32);
    *container = dds_alloc (sizeof (**container) * *Nu32);
    *rcontainer = dds_alloc (sizeof (**rcontainer) * *Nu32);

    for (uint32_t i = 0; i < *Nu32; ++i) {
        (*rcontainer)[i] = &(*container)[i];
        (*container)[i].usample = NULL;
    }
    return true;
}


samples* kscom_take(dds_entity_t reader_or_condition, long long N)
{
    uint32_t Nu32;
    dds_sample_info_t *info;
    kscom_sample_container_t *container, **rcontainer;
    samples *mysamples = malloc(sizeof(samples));
    mysamples->kdata = NULL;
    mysamples->ncount = 0;

    if (!readtake_pre (N, &Nu32, &info, &container, &rcontainer)){
        return mysamples;
    }

    dds_return_t sts = dds_take(reader_or_condition, (void **)rcontainer, info, Nu32, Nu32);

    if (sts <= 0)
    {
        mysamples->ncount = sts;
        return mysamples;
    }

    readtake_post (sts, info, container, rcontainer, mysamples);

    return mysamples;
}

dds_return_t kscom_dds_write(dds_entity_t writer, const void *data){

    return dds_write(writer, data);
}

dds_return_t kscom_set_status_mask(dds_entity_t entity, uint32_t mask){
    return dds_set_status_mask(entity,mask);
}

dds_return_t kscom_get_status_changes(dds_entity_t entity, uint32_t *status){
    return dds_get_status_changes(entity, status);
}

long  kscom_create_listener(){
    return (long)dds_create_listener(0);
}

long kscom_create_qos(){
    return (long) dds_create_qos();
}

void kscom_delete_qos(dds_qos_t* ptr){
    dds_delete_qos(ptr);
}

void kscom_set_reliability(dds_qos_t* qos, int reliability_kind, long blocking_time) {
    dds_qset_reliability(qos, reliability_kind, blocking_time);
}


void kscom_set_durability(dds_qos_t* qos, int durability_kind) {
    dds_qset_durability(qos, durability_kind);
}

void kscom_set_history(dds_qos_t* qos, int kind, int32_t depth){
    dds_qset_history(qos, kind, depth);
}

void kscom_set_resource_limits(dds_qos_t* qos, int32_t max_samples, int32_t max_instances, int32_t max_samples_per_intance){
    dds_qset_resource_limits(qos, max_samples, max_instances, max_samples_per_intance);
}

void kscom_set_presentation(dds_qos_t* qos, int32_t access_scope, bool coherent_access, bool ordered_access){
//    dds_qset_data_representation()
    dds_qset_presentation(qos,access_scope,coherent_access,ordered_access);
}

void kscom_set_lifespan(dds_qos_t* qos, int32_t lifespan){
    dds_qset_lifespan(qos, lifespan);
}

void kscom_set_deadline(dds_qos_t* qos, int32_t deadline){
    dds_qset_deadline(qos, deadline);
}

void kscom_set_latencybudget(dds_qos_t* qos, int32_t duration){
    dds_qset_latency_budget(qos, duration);
}

void kscom_set_ownership(dds_qos_t* qos, int32_t kind){
    dds_qset_ownership(qos, kind);
}

void kscom_set_ownership_strength(dds_qos_t* qos, int32_t value){
    dds_qset_ownership_strength(qos, value);
}

void kscom_set_liveliness(dds_qos_t* qos, int32_t kind, long lease_duration){
    dds_qset_liveliness(qos, kind, lease_duration);
}

void kscom_set_time_based_filter(dds_qos_t* qos, int32_t value){
    dds_qset_time_based_filter(qos, value);
}

void kscom_set_transport_priority(dds_qos_t* qos, int32_t kind){
    dds_qset_destination_order(qos, kind);
}

void kscom_set_destination_order(dds_qos_t* qos, int32_t kind){
    dds_qset_destination_order(qos, kind);
}

void kscom_set_writer_data_lifecycle(dds_qos_t* qos, bool autodispose){
    dds_qset_writer_data_lifecycle(qos, autodispose);
}

void kscom_set_reader_data_lifecycle(dds_qos_t* qos, long autopurge_nowriter_samples_delay, long autopurge_disposed_samples_delay){
    dds_qset_reader_data_lifecycle(qos, autopurge_nowriter_samples_delay, autopurge_disposed_samples_delay);
}

void kscom_set_durability_service(dds_qos_t *qos, dds_duration_t service_cleanup_delay, int history_kind, int32_t history_depth, int32_t max_samples, int32_t max_instances,int32_t max_samples_per_instance){
    dds_qset_durability_service(qos, service_cleanup_delay, history_kind, history_depth,max_samples,max_instances,max_samples_per_instance);
}

void kscom_set_userdata(dds_qos_t* qos, const void* value, size_t sz){
    dds_qset_userdata(qos, value, sz);
}

void kscom_set_topicdata(dds_qos_t* qos, const void* value, size_t sz){
    dds_qset_topicdata(qos, value, sz);
}

void kscom_set_groupdata(dds_qos_t* qos, const void* value, size_t sz){
    dds_qset_groupdata(qos, value, sz);
}

void kscom_set_ignorelocal(dds_qos_t* qos, int32_t ignore){
    dds_qset_ignorelocal(qos, ignore);
}

void kscom_set_property(dds_qos_t* qos, const char* name, const char* value){
    dds_qset_prop(qos, name, value);
}

void kscom_set_bproperty(dds_qos_t* qos, const char* name, const void* value, const size_t sz){
    dds_qset_bprop(qos, name, value, sz);
}

void kscom_set_typeconsistency(dds_qos_t* qos, int kind, bool ignore_sequence_bounds,
                               bool ignore_string_bounds,
                               bool ignore_member_names,
                               bool prevent_type_widening,
                               bool force_type_validation){
   dds_qset_type_consistency(qos, kind, ignore_sequence_bounds, ignore_string_bounds, ignore_member_names, prevent_type_widening, force_type_validation);
}

void kscom_set_data_representation(dds_qos_t* qos, int kind){
    if(kind == 0){
        dds_data_representation_id_t values[2];
        values[0] = 0;
        values[1] = 2;
        dds_qset_data_representation(qos, 2, values);
    }else if(kind == 1){
        dds_data_representation_id_t  value = 0;
        dds_qset_data_representation(qos, 1, &value);
    }else{
        dds_data_representation_id_t  value = 2;
        dds_qset_data_representation(qos, 1, &value);
    }
}

void kscom_set_entity_name(dds_qos_t* qos,const char* name){
    dds_qset_entity_name(qos,name);
}
#ifndef KSCOM_H
#define KSCOM_H

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <stdbool.h>
#include <stdint.h>

#include "dds/dds.h"
#include "dds/ddsrt/endian.h"
#include "dds/ddsrt/heap.h"
#include "dds/ddsrt/string.h"
#include "dds/ddsrt/mh3.h"
#include "dds/ddsrt/md5.h"
#include "dds/ddsi/ddsi_radmin.h"
#include "dds/ddsi/ddsi_serdata.h"
#include "dds/ddsi/ddsi_sertype.h"
#include "dds/ddsi/ddsi_typelib.h"
#include "dds/ddsi/ddsi_typebuilder.h"
#include "dds/cdr/dds_cdrstream.h"

typedef struct idl_type_desc {
    char * type_name;
    bool keyless;
    int version_support;
    uint8_t *xt_type_data;
} idl_type_desc_t;


typedef struct kscom_sample_container {
    void *usample;
    size_t usample_size;
} kscom_sample_container_t;


typedef struct sample {
    kscom_sample_container_t container;
    dds_sample_info_t info;
}sample;


typedef struct samples {
    sample* kdata;
    long long ncount;
}samples;

typedef struct sample_info{
    void* buf;
    size_t len;
}sample_info;

#ifdef __cplusplus
extern "C"{
#endif

dds_entity_t kscom_create_participant(const dds_domainid_t domain, const dds_qos_t *qos,
                                      const dds_listener_t *listener);




dds_entity_t kscom_create_publisher(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener);

dds_return_t kscom_suspend(dds_entity_t publisher);

dds_return_t kscom_resume(dds_entity_t publisher);

dds_return_t kscom_wait_for_acks(dds_entity_t publisher_or_writer, dds_duration_t timeout);

dds_entity_t kscom_create_subscriber(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener);

dds_return_t kscom_notify_readers(dds_entity_t subscriber);

dds_entity_t kscom_create_reader(dds_entity_t participant_or_subscriber, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener);

dds_entity_t kscom_create_topic(dds_entity_t participant, const char* topicname, idl_type_desc_t* desc, const dds_qos_t *qos, const dds_listener_t *listener);

dds_entity_t kscom_create_writer(dds_entity_t participant_or_publisher, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener);

dds_return_t kscom_get_mask(dds_entity_t condition, uint32_t *mask);

dds_return_t kscom_triggered(dds_entity_t entity);

dds_entity_t kscom_create_readcondition(dds_entity_t reader, uint32_t mask);

dds_entity_t kscom_create_querycondition(dds_entity_t reader, uint32_t mask, dds_querycondition_filter_fn filter);

dds_entity_t kscom_create_guardcondition(dds_entity_t owner);

dds_return_t kscom_set_guardcondition(dds_entity_t guardcond, bool triggered);

dds_return_t kscom_read_guardcondition(dds_entity_t guardcond, bool *triggered);

dds_return_t kscom_take_guardcondition(dds_entity_t guardcond, bool *triggered);

dds_entity_t kscom_create_waitset(dds_entity_t owner);

dds_return_t kscom_waitset_attach(dds_entity_t waitset, dds_entity_t entity, dds_attach_t x);

dds_return_t kscom_waitset_detach(dds_entity_t waitset, dds_entity_t entity);

dds_return_t kscom_waitset_wait(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_duration_t reltimeout);

dds_return_t kscom_waitset_wait_until(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_time_t abstimeout);

dds_return_t kscom_waitset_set_trigger(dds_entity_t waitset, bool trigger);

samples* kscom_take(dds_entity_t reader_or_condition, long long N);

dds_return_t kscom_dds_write(dds_entity_t writer, const void *data);

dds_return_t kscom_set_status_mask(dds_entity_t entity, uint32_t mask);

dds_return_t kscom_get_status_changes(dds_entity_t entity, uint32_t *status);
// sample* kscom_take_instance(dds_entity_t reader_or_condition, long long N);

long  kscom_create_listener();

long kscom_create_qos();

void kscom_delete_qos(dds_qos_t* qos);

// Reliability
void kscom_set_reliability(dds_qos_t* qos, int reliability_kind, long blocking_time);

//Durabiltiy
void kscom_set_durability(dds_qos_t* qos, int durability_kind);

void kscom_set_history(dds_qos_t* qos, int kind, int32_t depth);

void kscom_set_resource_limits(dds_qos_t* qos, int32_t max_samples, int32_t max_instances, int32_t max_samples_per_intance);

void kscom_set_presentation(dds_qos_t* qos, int32_t access_scope, bool coherent_access, bool ordered_access);

void kscom_set_lifespan(dds_qos_t* qos, int32_t lifespan);

void kscom_set_deadline(dds_qos_t* qos, int32_t deadline);

void kscom_set_latencybudget(dds_qos_t* qos, int32_t duration);

void kscom_set_ownership(dds_qos_t* qos, int32_t kind);

void kscom_set_ownership_strength(dds_qos_t* qos, int32_t value);

void kscom_set_liveliness(dds_qos_t* qos, int32_t kind, long lease_duration);

void kscom_set_time_based_filter(dds_qos_t* qos, int32_t value);

void kscom_set_transport_priority(dds_qos_t* qos, int32_t kind);

void kscom_set_destination_order(dds_qos_t* qos, int32_t kind);

void kscom_set_writer_data_lifecycle(dds_qos_t* qos, bool autodispose);

void kscom_set_reader_data_lifecycle(dds_qos_t* qos, long autopurge_nowriter_samples_delay, long autopurge_disposed_samples_delay);

void kscom_set_durability_service(dds_qos_t *qos, dds_duration_t service_cleanup_delay, int history_kind, int32_t history_depth, int32_t max_samples, int32_t max_instances,int32_t max_samples_per_instance);

void kscom_set_userdata(dds_qos_t* qos, const void* value, size_t sz);

void kscom_set_topicdata(dds_qos_t* qos, const void* value, size_t sz);

void kscom_set_groupdata(dds_qos_t* qos, const void* value, size_t sz);

void kscom_set_ignorelocal(dds_qos_t* qos, int32_t ignore);

void kscom_set_property(dds_qos_t* qos, const char* name, const char* value);

void kscom_set_bproperty(dds_qos_t* qos, const char* name, const void* value, size_t sz);

void kscom_set_typeconsistency(dds_qos_t* qos, int kind, bool ignore_sequence_bounds,bool ignore_string_bounds,bool ignore_member_names,bool prevent_type_widening,bool force_type_validation);

void kscom_set_data_representation(dds_qos_t* qos,int kind);

void kscom_set_entity_name(dds_qos_t* qos,const char* name);

#ifdef __cplusplus
}
#endif
#endif
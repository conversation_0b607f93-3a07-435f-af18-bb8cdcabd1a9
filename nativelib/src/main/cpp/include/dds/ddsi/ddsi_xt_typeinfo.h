/****************************************************************

  Generated by Eclipse Cyclone DDS IDL to C Translator
  File name: ddsi_xt_typeinfo.h
  Source: ddsi_xt_typeinfo.idl
  Cyclone DDS: V0.11.0

*****************************************************************/
#ifndef DDSI_XT_TYPEINFO_H
#define DDSI_XT_TYPEINFO_H

#include "dds/ddsc/dds_public_impl.h"
#include "dds/cdr/dds_cdrstream.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef uint8_t DDS_XTypes_EquivalenceKind;

#define DDS_XTypes_EquivalenceKind__alloc() \
((DDS_XTypes_EquivalenceKind*) dds_alloc (sizeof (DDS_XTypes_EquivalenceKind)));

#define DDS_XTypes_EK_MINIMAL 241
#define DDS_XTypes_EK_COMPLETE 242
#define DDS_XTypes_EK_BOTH 243
typedef uint8_t DDS_XTypes_TypeKind;

#define DDS_XTypes_TypeKind__alloc() \
((DDS_XTypes_TypeKind*) dds_alloc (sizeof (DDS_XTypes_TypeKind)));

#define DDS_XTypes_TK_NONE 0
#define DDS_XTypes_TK_BOOLEAN 1
#define DDS_XTypes_TK_BYTE 2
#define DDS_XTypes_TK_INT16 3
#define DDS_XTypes_TK_INT32 4
#define DDS_XTypes_TK_INT64 5
#define DDS_XTypes_TK_UINT16 6
#define DDS_XTypes_TK_UINT32 7
#define DDS_XTypes_TK_UINT64 8
#define DDS_XTypes_TK_FLOAT32 9
#define DDS_XTypes_TK_FLOAT64 10
#define DDS_XTypes_TK_FLOAT128 11
#define DDS_XTypes_TK_INT8 12
#define DDS_XTypes_TK_UINT8 13
#define DDS_XTypes_TK_CHAR8 16
#define DDS_XTypes_TK_CHAR16 17
#define DDS_XTypes_TK_STRING8 32
#define DDS_XTypes_TK_STRING16 33
#define DDS_XTypes_TK_ALIAS 48
#define DDS_XTypes_TK_ENUM 64
#define DDS_XTypes_TK_BITMASK 65
#define DDS_XTypes_TK_ANNOTATION 80
#define DDS_XTypes_TK_STRUCTURE 81
#define DDS_XTypes_TK_UNION 82
#define DDS_XTypes_TK_BITSET 83
#define DDS_XTypes_TK_SEQUENCE 96
#define DDS_XTypes_TK_ARRAY 97
#define DDS_XTypes_TK_MAP 98
typedef uint8_t DDS_XTypes_TypeIdentiferKind;

#define DDS_XTypes_TypeIdentiferKind__alloc() \
((DDS_XTypes_TypeIdentiferKind*) dds_alloc (sizeof (DDS_XTypes_TypeIdentiferKind)));

#define DDS_XTypes_TI_STRING8_SMALL 112
#define DDS_XTypes_TI_STRING8_LARGE 113
#define DDS_XTypes_TI_STRING16_SMALL 114
#define DDS_XTypes_TI_STRING16_LARGE 115
#define DDS_XTypes_TI_PLAIN_SEQUENCE_SMALL 128
#define DDS_XTypes_TI_PLAIN_SEQUENCE_LARGE 129
#define DDS_XTypes_TI_PLAIN_ARRAY_SMALL 144
#define DDS_XTypes_TI_PLAIN_ARRAY_LARGE 145
#define DDS_XTypes_TI_PLAIN_MAP_SMALL 160
#define DDS_XTypes_TI_PLAIN_MAP_LARGE 161
#define DDS_XTypes_TI_STRONGLY_CONNECTED_COMPONENT 176
#define DDS_XTypes_MEMBER_NAME_MAX_LENGTH 256
typedef char DDS_XTypes_MemberName[257];

#define DDS_XTypes_MemberName__alloc() \
((DDS_XTypes_MemberName*) dds_alloc (sizeof (DDS_XTypes_MemberName)));

#define DDS_XTypes_TYPE_NAME_MAX_LENGTH 256
typedef char DDS_XTypes_QualifiedTypeName[257];

#define DDS_XTypes_QualifiedTypeName__alloc() \
((DDS_XTypes_QualifiedTypeName*) dds_alloc (sizeof (DDS_XTypes_QualifiedTypeName)));

typedef uint8_t DDS_XTypes_PrimitiveTypeId;

#define DDS_XTypes_PrimitiveTypeId__alloc() \
((DDS_XTypes_PrimitiveTypeId*) dds_alloc (sizeof (DDS_XTypes_PrimitiveTypeId)));

typedef uint8_t DDS_XTypes_EquivalenceHash[14];

#define DDS_XTypes_EquivalenceHash__alloc() \
((DDS_XTypes_EquivalenceHash*) dds_alloc (sizeof (DDS_XTypes_EquivalenceHash)));

typedef uint8_t DDS_XTypes_NameHash[4];

#define DDS_XTypes_NameHash__alloc() \
((DDS_XTypes_NameHash*) dds_alloc (sizeof (DDS_XTypes_NameHash)));

typedef uint32_t DDS_XTypes_LBound;

#define DDS_XTypes_LBound__alloc() \
((DDS_XTypes_LBound*) dds_alloc (sizeof (DDS_XTypes_LBound)));

typedef struct DDS_XTypes_LBoundSeq
{
  uint32_t _maximum;
  uint32_t _length;
  DDS_XTypes_LBound *_buffer;
  bool _release;
} DDS_XTypes_LBoundSeq;

#define DDS_XTypes_LBoundSeq__alloc() \
((DDS_XTypes_LBoundSeq*) dds_alloc (sizeof (DDS_XTypes_LBoundSeq)));

#define DDS_XTypes_LBoundSeq_allocbuf(l) \
((DDS_XTypes_LBound *) dds_alloc ((l) * sizeof (DDS_XTypes_LBound)))
#define DDS_XTypes_INVALID_LBOUND 0
typedef uint8_t DDS_XTypes_SBound;

#define DDS_XTypes_SBound__alloc() \
((DDS_XTypes_SBound*) dds_alloc (sizeof (DDS_XTypes_SBound)));

typedef struct DDS_XTypes_SBoundSeq
{
  uint32_t _maximum;
  uint32_t _length;
  DDS_XTypes_SBound *_buffer;
  bool _release;
} DDS_XTypes_SBoundSeq;

#define DDS_XTypes_SBoundSeq__alloc() \
((DDS_XTypes_SBoundSeq*) dds_alloc (sizeof (DDS_XTypes_SBoundSeq)));

#define DDS_XTypes_SBoundSeq_allocbuf(l) \
((DDS_XTypes_SBound *) dds_alloc ((l) * sizeof (DDS_XTypes_SBound)))
#define DDS_XTypes_INVALID_SBOUND 0
typedef struct DDS_XTypes_TypeObjectHashId
{
  uint8_t _d;
  union
  {
    DDS_XTypes_EquivalenceHash hash;
  } _u;
} DDS_XTypes_TypeObjectHashId;

typedef uint16_t DDS_XTypes_MemberFlag;
#define DDS_XTypes_TRY_CONSTRUCT1 (1 << 0)
#define DDS_XTypes_TRY_CONSTRUCT2 (1 << 1)
#define DDS_XTypes_IS_EXTERNAL (1 << 2)
#define DDS_XTypes_IS_OPTIONAL (1 << 3)
#define DDS_XTypes_IS_MUST_UNDERSTAND (1 << 4)
#define DDS_XTypes_IS_KEY (1 << 5)
#define DDS_XTypes_IS_DEFAULT (1 << 6)
typedef DDS_XTypes_MemberFlag DDS_XTypes_CollectionElementFlag;

#define DDS_XTypes_CollectionElementFlag__alloc() \
((DDS_XTypes_CollectionElementFlag*) dds_alloc (sizeof (DDS_XTypes_CollectionElementFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_StructMemberFlag;

#define DDS_XTypes_StructMemberFlag__alloc() \
((DDS_XTypes_StructMemberFlag*) dds_alloc (sizeof (DDS_XTypes_StructMemberFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_UnionMemberFlag;

#define DDS_XTypes_UnionMemberFlag__alloc() \
((DDS_XTypes_UnionMemberFlag*) dds_alloc (sizeof (DDS_XTypes_UnionMemberFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_UnionDiscriminatorFlag;

#define DDS_XTypes_UnionDiscriminatorFlag__alloc() \
((DDS_XTypes_UnionDiscriminatorFlag*) dds_alloc (sizeof (DDS_XTypes_UnionDiscriminatorFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_EnumeratedLiteralFlag;

#define DDS_XTypes_EnumeratedLiteralFlag__alloc() \
((DDS_XTypes_EnumeratedLiteralFlag*) dds_alloc (sizeof (DDS_XTypes_EnumeratedLiteralFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_AnnotationParameterFlag;

#define DDS_XTypes_AnnotationParameterFlag__alloc() \
((DDS_XTypes_AnnotationParameterFlag*) dds_alloc (sizeof (DDS_XTypes_AnnotationParameterFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_AliasMemberFlag;

#define DDS_XTypes_AliasMemberFlag__alloc() \
((DDS_XTypes_AliasMemberFlag*) dds_alloc (sizeof (DDS_XTypes_AliasMemberFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_BitflagFlag;

#define DDS_XTypes_BitflagFlag__alloc() \
((DDS_XTypes_BitflagFlag*) dds_alloc (sizeof (DDS_XTypes_BitflagFlag)));

typedef DDS_XTypes_MemberFlag DDS_XTypes_BitsetMemberFlag;

#define DDS_XTypes_BitsetMemberFlag__alloc() \
((DDS_XTypes_BitsetMemberFlag*) dds_alloc (sizeof (DDS_XTypes_BitsetMemberFlag)));

#define DDS_XTypes_MemberFlagMinimalMask 63
typedef uint16_t DDS_XTypes_TypeFlag;
#define DDS_XTypes_IS_FINAL (1 << 0)
#define DDS_XTypes_IS_APPENDABLE (1 << 1)
#define DDS_XTypes_IS_MUTABLE (1 << 2)
#define DDS_XTypes_IS_NESTED (1 << 3)
#define DDS_XTypes_IS_AUTOID_HASH (1 << 4)
typedef DDS_XTypes_TypeFlag DDS_XTypes_StructTypeFlag;

#define DDS_XTypes_StructTypeFlag__alloc() \
((DDS_XTypes_StructTypeFlag*) dds_alloc (sizeof (DDS_XTypes_StructTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_UnionTypeFlag;

#define DDS_XTypes_UnionTypeFlag__alloc() \
((DDS_XTypes_UnionTypeFlag*) dds_alloc (sizeof (DDS_XTypes_UnionTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_CollectionTypeFlag;

#define DDS_XTypes_CollectionTypeFlag__alloc() \
((DDS_XTypes_CollectionTypeFlag*) dds_alloc (sizeof (DDS_XTypes_CollectionTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_AnnotationTypeFlag;

#define DDS_XTypes_AnnotationTypeFlag__alloc() \
((DDS_XTypes_AnnotationTypeFlag*) dds_alloc (sizeof (DDS_XTypes_AnnotationTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_AliasTypeFlag;

#define DDS_XTypes_AliasTypeFlag__alloc() \
((DDS_XTypes_AliasTypeFlag*) dds_alloc (sizeof (DDS_XTypes_AliasTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_EnumTypeFlag;

#define DDS_XTypes_EnumTypeFlag__alloc() \
((DDS_XTypes_EnumTypeFlag*) dds_alloc (sizeof (DDS_XTypes_EnumTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_BitmaskTypeFlag;

#define DDS_XTypes_BitmaskTypeFlag__alloc() \
((DDS_XTypes_BitmaskTypeFlag*) dds_alloc (sizeof (DDS_XTypes_BitmaskTypeFlag)));

typedef DDS_XTypes_TypeFlag DDS_XTypes_BitsetTypeFlag;

#define DDS_XTypes_BitsetTypeFlag__alloc() \
((DDS_XTypes_BitsetTypeFlag*) dds_alloc (sizeof (DDS_XTypes_BitsetTypeFlag)));

#define DDS_XTypes_TypeFlagMinimalMask 7
struct DDS_XTypes_TypeIdentifier;
typedef struct DDS_XTypes_StringSTypeDefn
{
  DDS_XTypes_SBound bound;
} DDS_XTypes_StringSTypeDefn;

typedef struct DDS_XTypes_StringLTypeDefn
{
  DDS_XTypes_LBound bound;
} DDS_XTypes_StringLTypeDefn;

typedef struct DDS_XTypes_PlainCollectionHeader
{
  DDS_XTypes_EquivalenceKind equiv_kind;
  DDS_XTypes_CollectionElementFlag element_flags;
} DDS_XTypes_PlainCollectionHeader;

typedef struct DDS_XTypes_PlainSequenceSElemDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_SBound bound;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
} DDS_XTypes_PlainSequenceSElemDefn;

typedef struct DDS_XTypes_PlainSequenceLElemDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_LBound bound;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
} DDS_XTypes_PlainSequenceLElemDefn;

typedef struct DDS_XTypes_PlainArraySElemDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_SBoundSeq array_bound_seq;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
} DDS_XTypes_PlainArraySElemDefn;

typedef struct DDS_XTypes_PlainArrayLElemDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_LBoundSeq array_bound_seq;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
} DDS_XTypes_PlainArrayLElemDefn;

typedef struct DDS_XTypes_PlainMapSTypeDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_SBound bound;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
  DDS_XTypes_CollectionElementFlag key_flags;
  struct DDS_XTypes_TypeIdentifier * key_identifier;
} DDS_XTypes_PlainMapSTypeDefn;

typedef struct DDS_XTypes_PlainMapLTypeDefn
{
  struct DDS_XTypes_PlainCollectionHeader header;
  DDS_XTypes_LBound bound;
  struct DDS_XTypes_TypeIdentifier * element_identifier;
  DDS_XTypes_CollectionElementFlag key_flags;
  struct DDS_XTypes_TypeIdentifier * key_identifier;
} DDS_XTypes_PlainMapLTypeDefn;

typedef struct DDS_XTypes_StronglyConnectedComponentId
{
  struct DDS_XTypes_TypeObjectHashId sc_component_id;
  int32_t scc_length;
  int32_t scc_index;
} DDS_XTypes_StronglyConnectedComponentId;

#if 0 /* empty struct */
typedef struct DDS_XTypes_ExtendedTypeDefn
{
} DDS_XTypes_ExtendedTypeDefn;
#endif /* empty struct */

typedef struct DDS_XTypes_TypeIdentifier
{
  uint8_t _d;
  union
  {
    struct DDS_XTypes_StringSTypeDefn string_sdefn;
    struct DDS_XTypes_StringLTypeDefn string_ldefn;
    struct DDS_XTypes_PlainSequenceSElemDefn seq_sdefn;
    struct DDS_XTypes_PlainSequenceLElemDefn seq_ldefn;
    struct DDS_XTypes_PlainArraySElemDefn array_sdefn;
    struct DDS_XTypes_PlainArrayLElemDefn array_ldefn;
    struct DDS_XTypes_PlainMapSTypeDefn map_sdefn;
    struct DDS_XTypes_PlainMapLTypeDefn map_ldefn;
    struct DDS_XTypes_StronglyConnectedComponentId sc_component_id;
    DDS_XTypes_EquivalenceHash equivalence_hash;
  } _u;
} DDS_XTypes_TypeIdentifier;

DDS_EXPORT extern const dds_topic_descriptor_t DDS_XTypes_TypeIdentifier_desc;

#define DDS_XTypes_TypeIdentifier__alloc() \
((DDS_XTypes_TypeIdentifier*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifier)));

#define DDS_XTypes_TypeIdentifier_free(d,o) \
dds_sample_free ((d), &DDS_XTypes_TypeIdentifier_desc, (o))

DDS_EXPORT extern const struct dds_cdrstream_desc DDS_XTypes_TypeIdentifier_cdrstream_desc;

typedef struct DDS_XTypes_TypeIdentifierSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifier *_buffer;
  bool _release;
} DDS_XTypes_TypeIdentifierSeq;

#define DDS_XTypes_TypeIdentifierSeq__alloc() \
((DDS_XTypes_TypeIdentifierSeq*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifierSeq)));

#define DDS_XTypes_TypeIdentifierSeq_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifier *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifier)))
typedef uint32_t DDS_XTypes_MemberId;

#define DDS_XTypes_MemberId__alloc() \
((DDS_XTypes_MemberId*) dds_alloc (sizeof (DDS_XTypes_MemberId)));

#define DDS_XTypes_ANNOTATION_STR_VALUE_MAX_LEN 128
#define DDS_XTypes_ANNOTATION_OCTETSEC_VALUE_MAX_LEN 128
#if 0 /* empty struct */
typedef struct DDS_XTypes_ExtendedAnnotationParameterValue
{
} DDS_XTypes_ExtendedAnnotationParameterValue;
#endif /* empty struct */

typedef struct DDS_XTypes_AnnotationParameterValue
{
  uint8_t _d;
  union
  {
    bool boolean_value;
    uint8_t byte_value;
    int8_t int8_value;
    uint8_t uint8_value;
    int16_t int16_value;
    uint16_t uint_16_value;
    int32_t int32_value;
    uint32_t uint32_value;
    int64_t int64_value;
    uint64_t uint64_value;
    float float32_value;
    double float64_value;
    char char_value;
    int32_t enumerated_value;
    char string8_value[129];
    /* struct DDS_XTypes_ExtendedAnnotationParameterValue extended_value */ /* no members */
  } _u;
} DDS_XTypes_AnnotationParameterValue;

typedef struct DDS_XTypes_AppliedAnnotationParameter
{
  DDS_XTypes_NameHash paramname_hash;
  struct DDS_XTypes_AnnotationParameterValue value;
} DDS_XTypes_AppliedAnnotationParameter;

typedef struct DDS_XTypes_AppliedAnnotationParameterSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_AppliedAnnotationParameter *_buffer;
  bool _release;
} DDS_XTypes_AppliedAnnotationParameterSeq;

#define DDS_XTypes_AppliedAnnotationParameterSeq__alloc() \
((DDS_XTypes_AppliedAnnotationParameterSeq*) dds_alloc (sizeof (DDS_XTypes_AppliedAnnotationParameterSeq)));

#define DDS_XTypes_AppliedAnnotationParameterSeq_allocbuf(l) \
((struct DDS_XTypes_AppliedAnnotationParameter *) dds_alloc ((l) * sizeof (struct DDS_XTypes_AppliedAnnotationParameter)))
typedef struct DDS_XTypes_AppliedAnnotation
{
  struct DDS_XTypes_TypeIdentifier annotation_typeid;
  DDS_XTypes_AppliedAnnotationParameterSeq * param_seq;
} DDS_XTypes_AppliedAnnotation;

typedef struct DDS_XTypes_AppliedAnnotationSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_AppliedAnnotation *_buffer;
  bool _release;
} DDS_XTypes_AppliedAnnotationSeq;

#define DDS_XTypes_AppliedAnnotationSeq__alloc() \
((DDS_XTypes_AppliedAnnotationSeq*) dds_alloc (sizeof (DDS_XTypes_AppliedAnnotationSeq)));

#define DDS_XTypes_AppliedAnnotationSeq_allocbuf(l) \
((struct DDS_XTypes_AppliedAnnotation *) dds_alloc ((l) * sizeof (struct DDS_XTypes_AppliedAnnotation)))
typedef struct DDS_XTypes_AppliedVerbatimAnnotation
{
  char placement[33];
  char language[33];
  char * text;
} DDS_XTypes_AppliedVerbatimAnnotation;

typedef struct DDS_XTypes_AppliedBuiltinMemberAnnotations
{
  char * unit;
  struct DDS_XTypes_AnnotationParameterValue * min;
  struct DDS_XTypes_AnnotationParameterValue * max;
  char * hash_id;
} DDS_XTypes_AppliedBuiltinMemberAnnotations;

typedef struct DDS_XTypes_CommonStructMember
{
  DDS_XTypes_MemberId member_id;
  DDS_XTypes_StructMemberFlag member_flags;
  struct DDS_XTypes_TypeIdentifier member_type_id;
} DDS_XTypes_CommonStructMember;

typedef struct DDS_XTypes_CompleteMemberDetail
{
  DDS_XTypes_MemberName name;
  struct DDS_XTypes_AppliedBuiltinMemberAnnotations * ann_builtin;
  DDS_XTypes_AppliedAnnotationSeq * ann_custom;
} DDS_XTypes_CompleteMemberDetail;

typedef struct DDS_XTypes_MinimalMemberDetail
{
  DDS_XTypes_NameHash name_hash;
} DDS_XTypes_MinimalMemberDetail;

typedef struct DDS_XTypes_CompleteStructMember
{
  struct DDS_XTypes_CommonStructMember common;
  struct DDS_XTypes_CompleteMemberDetail detail;
} DDS_XTypes_CompleteStructMember;

typedef struct DDS_XTypes_CompleteStructMemberSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteStructMember *_buffer;
  bool _release;
} DDS_XTypes_CompleteStructMemberSeq;

#define DDS_XTypes_CompleteStructMemberSeq__alloc() \
((DDS_XTypes_CompleteStructMemberSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteStructMemberSeq)));

#define DDS_XTypes_CompleteStructMemberSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteStructMember *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteStructMember)))
typedef struct DDS_XTypes_MinimalStructMember
{
  struct DDS_XTypes_CommonStructMember common;
  struct DDS_XTypes_MinimalMemberDetail detail;
} DDS_XTypes_MinimalStructMember;

typedef struct DDS_XTypes_MinimalStructMemberSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalStructMember *_buffer;
  bool _release;
} DDS_XTypes_MinimalStructMemberSeq;

#define DDS_XTypes_MinimalStructMemberSeq__alloc() \
((DDS_XTypes_MinimalStructMemberSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalStructMemberSeq)));

#define DDS_XTypes_MinimalStructMemberSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalStructMember *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalStructMember)))
typedef struct DDS_XTypes_AppliedBuiltinTypeAnnotations
{
  struct DDS_XTypes_AppliedVerbatimAnnotation * verbatim;
} DDS_XTypes_AppliedBuiltinTypeAnnotations;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalTypeDetail
{
} DDS_XTypes_MinimalTypeDetail;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteTypeDetail
{
  struct DDS_XTypes_AppliedBuiltinTypeAnnotations * ann_builtin;
  DDS_XTypes_AppliedAnnotationSeq * ann_custom;
  DDS_XTypes_QualifiedTypeName type_name;
} DDS_XTypes_CompleteTypeDetail;

typedef struct DDS_XTypes_CompleteStructHeader
{
  struct DDS_XTypes_TypeIdentifier base_type;
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteStructHeader;

typedef struct DDS_XTypes_MinimalStructHeader
{
  struct DDS_XTypes_TypeIdentifier base_type;
  /* struct DDS_XTypes_MinimalTypeDetail detail */ /* no members */
} DDS_XTypes_MinimalStructHeader;

typedef struct DDS_XTypes_CompleteStructType
{
  DDS_XTypes_StructTypeFlag struct_flags;
  struct DDS_XTypes_CompleteStructHeader header;
  DDS_XTypes_CompleteStructMemberSeq member_seq;
} DDS_XTypes_CompleteStructType;

typedef struct DDS_XTypes_MinimalStructType
{
  DDS_XTypes_StructTypeFlag struct_flags;
  struct DDS_XTypes_MinimalStructHeader header;
  DDS_XTypes_MinimalStructMemberSeq member_seq;
} DDS_XTypes_MinimalStructType;

typedef struct DDS_XTypes_UnionCaseLabelSeq
{
  uint32_t _maximum;
  uint32_t _length;
  int32_t *_buffer;
  bool _release;
} DDS_XTypes_UnionCaseLabelSeq;

#define DDS_XTypes_UnionCaseLabelSeq__alloc() \
((DDS_XTypes_UnionCaseLabelSeq*) dds_alloc (sizeof (DDS_XTypes_UnionCaseLabelSeq)));

#define DDS_XTypes_UnionCaseLabelSeq_allocbuf(l) \
((int32_t *) dds_alloc ((l) * sizeof (int32_t)))
typedef struct DDS_XTypes_CommonUnionMember
{
  DDS_XTypes_MemberId member_id;
  DDS_XTypes_UnionMemberFlag member_flags;
  struct DDS_XTypes_TypeIdentifier type_id;
  DDS_XTypes_UnionCaseLabelSeq label_seq;
} DDS_XTypes_CommonUnionMember;

typedef struct DDS_XTypes_CompleteUnionMember
{
  struct DDS_XTypes_CommonUnionMember common;
  struct DDS_XTypes_CompleteMemberDetail detail;
} DDS_XTypes_CompleteUnionMember;

typedef struct DDS_XTypes_CompleteUnionMemberSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteUnionMember *_buffer;
  bool _release;
} DDS_XTypes_CompleteUnionMemberSeq;

#define DDS_XTypes_CompleteUnionMemberSeq__alloc() \
((DDS_XTypes_CompleteUnionMemberSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteUnionMemberSeq)));

#define DDS_XTypes_CompleteUnionMemberSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteUnionMember *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteUnionMember)))
typedef struct DDS_XTypes_MinimalUnionMember
{
  struct DDS_XTypes_CommonUnionMember common;
  struct DDS_XTypes_MinimalMemberDetail detail;
} DDS_XTypes_MinimalUnionMember;

typedef struct DDS_XTypes_MinimalUnionMemberSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalUnionMember *_buffer;
  bool _release;
} DDS_XTypes_MinimalUnionMemberSeq;

#define DDS_XTypes_MinimalUnionMemberSeq__alloc() \
((DDS_XTypes_MinimalUnionMemberSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalUnionMemberSeq)));

#define DDS_XTypes_MinimalUnionMemberSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalUnionMember *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalUnionMember)))
typedef struct DDS_XTypes_CommonDiscriminatorMember
{
  DDS_XTypes_UnionDiscriminatorFlag member_flags;
  struct DDS_XTypes_TypeIdentifier type_id;
} DDS_XTypes_CommonDiscriminatorMember;

typedef struct DDS_XTypes_CompleteDiscriminatorMember
{
  struct DDS_XTypes_CommonDiscriminatorMember common;
  struct DDS_XTypes_AppliedBuiltinTypeAnnotations * ann_builtin;
  DDS_XTypes_AppliedAnnotationSeq * ann_custom;
} DDS_XTypes_CompleteDiscriminatorMember;

typedef struct DDS_XTypes_MinimalDiscriminatorMember
{
  struct DDS_XTypes_CommonDiscriminatorMember common;
} DDS_XTypes_MinimalDiscriminatorMember;

typedef struct DDS_XTypes_CompleteUnionHeader
{
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteUnionHeader;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalUnionHeader
{
  /* struct DDS_XTypes_MinimalTypeDetail detail */ /* no members */
} DDS_XTypes_MinimalUnionHeader;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteUnionType
{
  DDS_XTypes_UnionTypeFlag union_flags;
  struct DDS_XTypes_CompleteUnionHeader header;
  struct DDS_XTypes_CompleteDiscriminatorMember discriminator;
  DDS_XTypes_CompleteUnionMemberSeq member_seq;
} DDS_XTypes_CompleteUnionType;

typedef struct DDS_XTypes_MinimalUnionType
{
  DDS_XTypes_UnionTypeFlag union_flags;
  /* struct DDS_XTypes_MinimalUnionHeader header */ /* no members */
  struct DDS_XTypes_MinimalDiscriminatorMember discriminator;
  DDS_XTypes_MinimalUnionMemberSeq member_seq;
} DDS_XTypes_MinimalUnionType;

typedef struct DDS_XTypes_CommonAnnotationParameter
{
  DDS_XTypes_AnnotationParameterFlag member_flags;
  struct DDS_XTypes_TypeIdentifier member_type_id;
} DDS_XTypes_CommonAnnotationParameter;

typedef struct DDS_XTypes_CompleteAnnotationParameter
{
  struct DDS_XTypes_CommonAnnotationParameter common;
  DDS_XTypes_MemberName name;
  struct DDS_XTypes_AnnotationParameterValue default_value;
} DDS_XTypes_CompleteAnnotationParameter;

typedef struct DDS_XTypes_CompleteAnnotationParameterSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteAnnotationParameter *_buffer;
  bool _release;
} DDS_XTypes_CompleteAnnotationParameterSeq;

#define DDS_XTypes_CompleteAnnotationParameterSeq__alloc() \
((DDS_XTypes_CompleteAnnotationParameterSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteAnnotationParameterSeq)));

#define DDS_XTypes_CompleteAnnotationParameterSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteAnnotationParameter *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteAnnotationParameter)))
typedef struct DDS_XTypes_MinimalAnnotationParameter
{
  struct DDS_XTypes_CommonAnnotationParameter common;
  DDS_XTypes_NameHash name_hash;
  struct DDS_XTypes_AnnotationParameterValue default_value;
} DDS_XTypes_MinimalAnnotationParameter;

typedef struct DDS_XTypes_MinimalAnnotationParameterSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalAnnotationParameter *_buffer;
  bool _release;
} DDS_XTypes_MinimalAnnotationParameterSeq;

#define DDS_XTypes_MinimalAnnotationParameterSeq__alloc() \
((DDS_XTypes_MinimalAnnotationParameterSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalAnnotationParameterSeq)));

#define DDS_XTypes_MinimalAnnotationParameterSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalAnnotationParameter *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalAnnotationParameter)))
typedef struct DDS_XTypes_CompleteAnnotationHeader
{
  DDS_XTypes_QualifiedTypeName annotation_name;
} DDS_XTypes_CompleteAnnotationHeader;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalAnnotationHeader
{
} DDS_XTypes_MinimalAnnotationHeader;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteAnnotationType
{
  DDS_XTypes_AnnotationTypeFlag annotation_flag;
  struct DDS_XTypes_CompleteAnnotationHeader header;
  DDS_XTypes_CompleteAnnotationParameterSeq member_seq;
} DDS_XTypes_CompleteAnnotationType;

typedef struct DDS_XTypes_MinimalAnnotationType
{
  DDS_XTypes_AnnotationTypeFlag annotation_flag;
  /* struct DDS_XTypes_MinimalAnnotationHeader header */ /* no members */
  DDS_XTypes_MinimalAnnotationParameterSeq member_seq;
} DDS_XTypes_MinimalAnnotationType;

typedef struct DDS_XTypes_CommonAliasBody
{
  DDS_XTypes_AliasMemberFlag related_flags;
  struct DDS_XTypes_TypeIdentifier related_type;
} DDS_XTypes_CommonAliasBody;

typedef struct DDS_XTypes_CompleteAliasBody
{
  struct DDS_XTypes_CommonAliasBody common;
  struct DDS_XTypes_AppliedBuiltinMemberAnnotations * ann_builtin;
  DDS_XTypes_AppliedAnnotationSeq * ann_custom;
} DDS_XTypes_CompleteAliasBody;

typedef struct DDS_XTypes_MinimalAliasBody
{
  struct DDS_XTypes_CommonAliasBody common;
} DDS_XTypes_MinimalAliasBody;

typedef struct DDS_XTypes_CompleteAliasHeader
{
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteAliasHeader;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalAliasHeader
{
} DDS_XTypes_MinimalAliasHeader;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteAliasType
{
  DDS_XTypes_AliasTypeFlag alias_flags;
  struct DDS_XTypes_CompleteAliasHeader header;
  struct DDS_XTypes_CompleteAliasBody body;
} DDS_XTypes_CompleteAliasType;

typedef struct DDS_XTypes_MinimalAliasType
{
  DDS_XTypes_AliasTypeFlag alias_flags;
  /* struct DDS_XTypes_MinimalAliasHeader header */ /* no members */
  struct DDS_XTypes_MinimalAliasBody body;
} DDS_XTypes_MinimalAliasType;

typedef struct DDS_XTypes_CompleteElementDetail
{
  struct DDS_XTypes_AppliedBuiltinMemberAnnotations * ann_builtin;
  DDS_XTypes_AppliedAnnotationSeq * ann_custom;
} DDS_XTypes_CompleteElementDetail;

typedef struct DDS_XTypes_CommonCollectionElement
{
  DDS_XTypes_CollectionElementFlag element_flags;
  struct DDS_XTypes_TypeIdentifier type;
} DDS_XTypes_CommonCollectionElement;

typedef struct DDS_XTypes_CompleteCollectionElement
{
  struct DDS_XTypes_CommonCollectionElement common;
  struct DDS_XTypes_CompleteElementDetail detail;
} DDS_XTypes_CompleteCollectionElement;

typedef struct DDS_XTypes_MinimalCollectionElement
{
  struct DDS_XTypes_CommonCollectionElement common;
} DDS_XTypes_MinimalCollectionElement;

typedef struct DDS_XTypes_CommonCollectionHeader
{
  DDS_XTypes_LBound bound;
} DDS_XTypes_CommonCollectionHeader;

typedef struct DDS_XTypes_CompleteCollectionHeader
{
  struct DDS_XTypes_CommonCollectionHeader common;
  struct DDS_XTypes_CompleteTypeDetail * detail;
} DDS_XTypes_CompleteCollectionHeader;

typedef struct DDS_XTypes_MinimalCollectionHeader
{
  struct DDS_XTypes_CommonCollectionHeader common;
} DDS_XTypes_MinimalCollectionHeader;

typedef struct DDS_XTypes_CompleteSequenceType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_CompleteCollectionHeader header;
  struct DDS_XTypes_CompleteCollectionElement element;
} DDS_XTypes_CompleteSequenceType;

typedef struct DDS_XTypes_MinimalSequenceType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_MinimalCollectionHeader header;
  struct DDS_XTypes_MinimalCollectionElement element;
} DDS_XTypes_MinimalSequenceType;

typedef struct DDS_XTypes_CommonArrayHeader
{
  DDS_XTypes_LBoundSeq bound_seq;
} DDS_XTypes_CommonArrayHeader;

typedef struct DDS_XTypes_CompleteArrayHeader
{
  struct DDS_XTypes_CommonArrayHeader common;
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteArrayHeader;

typedef struct DDS_XTypes_MinimalArrayHeader
{
  struct DDS_XTypes_CommonArrayHeader common;
} DDS_XTypes_MinimalArrayHeader;

typedef struct DDS_XTypes_CompleteArrayType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_CompleteArrayHeader header;
  struct DDS_XTypes_CompleteCollectionElement element;
} DDS_XTypes_CompleteArrayType;

typedef struct DDS_XTypes_MinimalArrayType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_MinimalArrayHeader header;
  struct DDS_XTypes_MinimalCollectionElement element;
} DDS_XTypes_MinimalArrayType;

typedef struct DDS_XTypes_CompleteMapType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_CompleteCollectionHeader header;
  struct DDS_XTypes_CompleteCollectionElement key;
  struct DDS_XTypes_CompleteCollectionElement element;
} DDS_XTypes_CompleteMapType;

typedef struct DDS_XTypes_MinimalMapType
{
  DDS_XTypes_CollectionTypeFlag collection_flag;
  struct DDS_XTypes_MinimalCollectionHeader header;
  struct DDS_XTypes_MinimalCollectionElement key;
  struct DDS_XTypes_MinimalCollectionElement element;
} DDS_XTypes_MinimalMapType;

typedef uint16_t DDS_XTypes_BitBound;

#define DDS_XTypes_BitBound__alloc() \
((DDS_XTypes_BitBound*) dds_alloc (sizeof (DDS_XTypes_BitBound)));

typedef struct DDS_XTypes_CommonEnumeratedLiteral
{
  int32_t value;
  DDS_XTypes_EnumeratedLiteralFlag flags;
} DDS_XTypes_CommonEnumeratedLiteral;

typedef struct DDS_XTypes_CompleteEnumeratedLiteral
{
  struct DDS_XTypes_CommonEnumeratedLiteral common;
  struct DDS_XTypes_CompleteMemberDetail detail;
} DDS_XTypes_CompleteEnumeratedLiteral;

typedef struct DDS_XTypes_CompleteEnumeratedLiteralSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteEnumeratedLiteral *_buffer;
  bool _release;
} DDS_XTypes_CompleteEnumeratedLiteralSeq;

#define DDS_XTypes_CompleteEnumeratedLiteralSeq__alloc() \
((DDS_XTypes_CompleteEnumeratedLiteralSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteEnumeratedLiteralSeq)));

#define DDS_XTypes_CompleteEnumeratedLiteralSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteEnumeratedLiteral *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteEnumeratedLiteral)))
typedef struct DDS_XTypes_MinimalEnumeratedLiteral
{
  struct DDS_XTypes_CommonEnumeratedLiteral common;
  struct DDS_XTypes_MinimalMemberDetail detail;
} DDS_XTypes_MinimalEnumeratedLiteral;

typedef struct DDS_XTypes_MinimalEnumeratedLiteralSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalEnumeratedLiteral *_buffer;
  bool _release;
} DDS_XTypes_MinimalEnumeratedLiteralSeq;

#define DDS_XTypes_MinimalEnumeratedLiteralSeq__alloc() \
((DDS_XTypes_MinimalEnumeratedLiteralSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalEnumeratedLiteralSeq)));

#define DDS_XTypes_MinimalEnumeratedLiteralSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalEnumeratedLiteral *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalEnumeratedLiteral)))
typedef struct DDS_XTypes_CommonEnumeratedHeader
{
  DDS_XTypes_BitBound bit_bound;
} DDS_XTypes_CommonEnumeratedHeader;

typedef struct DDS_XTypes_CompleteEnumeratedHeader
{
  struct DDS_XTypes_CommonEnumeratedHeader common;
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteEnumeratedHeader;

typedef struct DDS_XTypes_MinimalEnumeratedHeader
{
  struct DDS_XTypes_CommonEnumeratedHeader common;
} DDS_XTypes_MinimalEnumeratedHeader;

typedef struct DDS_XTypes_CompleteEnumeratedType
{
  DDS_XTypes_EnumTypeFlag enum_flags;
  struct DDS_XTypes_CompleteEnumeratedHeader header;
  DDS_XTypes_CompleteEnumeratedLiteralSeq literal_seq;
} DDS_XTypes_CompleteEnumeratedType;

typedef struct DDS_XTypes_MinimalEnumeratedType
{
  DDS_XTypes_EnumTypeFlag enum_flags;
  struct DDS_XTypes_MinimalEnumeratedHeader header;
  DDS_XTypes_MinimalEnumeratedLiteralSeq literal_seq;
} DDS_XTypes_MinimalEnumeratedType;

typedef struct DDS_XTypes_CommonBitflag
{
  uint16_t position;
  DDS_XTypes_BitflagFlag flags;
} DDS_XTypes_CommonBitflag;

typedef struct DDS_XTypes_CompleteBitflag
{
  struct DDS_XTypes_CommonBitflag common;
  struct DDS_XTypes_CompleteMemberDetail detail;
} DDS_XTypes_CompleteBitflag;

typedef struct DDS_XTypes_CompleteBitflagSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteBitflag *_buffer;
  bool _release;
} DDS_XTypes_CompleteBitflagSeq;

#define DDS_XTypes_CompleteBitflagSeq__alloc() \
((DDS_XTypes_CompleteBitflagSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteBitflagSeq)));

#define DDS_XTypes_CompleteBitflagSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteBitflag *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteBitflag)))
typedef struct DDS_XTypes_MinimalBitflag
{
  struct DDS_XTypes_CommonBitflag common;
  struct DDS_XTypes_MinimalMemberDetail detail;
} DDS_XTypes_MinimalBitflag;

typedef struct DDS_XTypes_MinimalBitflagSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalBitflag *_buffer;
  bool _release;
} DDS_XTypes_MinimalBitflagSeq;

#define DDS_XTypes_MinimalBitflagSeq__alloc() \
((DDS_XTypes_MinimalBitflagSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalBitflagSeq)));

#define DDS_XTypes_MinimalBitflagSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalBitflag *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalBitflag)))
typedef struct DDS_XTypes_CommonBitmaskHeader
{
  DDS_XTypes_BitBound bit_bound;
} DDS_XTypes_CommonBitmaskHeader;

typedef DDS_XTypes_CompleteEnumeratedHeader DDS_XTypes_CompleteBitmaskHeader;

#define DDS_XTypes_CompleteBitmaskHeader__alloc() \
((DDS_XTypes_CompleteBitmaskHeader*) dds_alloc (sizeof (DDS_XTypes_CompleteBitmaskHeader)));

typedef DDS_XTypes_MinimalEnumeratedHeader DDS_XTypes_MinimalBitmaskHeader;

#define DDS_XTypes_MinimalBitmaskHeader__alloc() \
((DDS_XTypes_MinimalBitmaskHeader*) dds_alloc (sizeof (DDS_XTypes_MinimalBitmaskHeader)));

typedef struct DDS_XTypes_CompleteBitmaskType
{
  DDS_XTypes_BitmaskTypeFlag bitmask_flags;
  DDS_XTypes_CompleteBitmaskHeader header;
  DDS_XTypes_CompleteBitflagSeq flag_seq;
} DDS_XTypes_CompleteBitmaskType;

typedef struct DDS_XTypes_MinimalBitmaskType
{
  DDS_XTypes_BitmaskTypeFlag bitmask_flags;
  DDS_XTypes_MinimalBitmaskHeader header;
  DDS_XTypes_MinimalBitflagSeq flag_seq;
} DDS_XTypes_MinimalBitmaskType;

typedef struct DDS_XTypes_CommonBitfield
{
  uint16_t position;
  DDS_XTypes_BitsetMemberFlag flags;
  uint8_t bitcount;
  DDS_XTypes_TypeKind holder_type;
} DDS_XTypes_CommonBitfield;

typedef struct DDS_XTypes_CompleteBitfield
{
  struct DDS_XTypes_CommonBitfield common;
  struct DDS_XTypes_CompleteMemberDetail detail;
} DDS_XTypes_CompleteBitfield;

typedef struct DDS_XTypes_CompleteBitfieldSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_CompleteBitfield *_buffer;
  bool _release;
} DDS_XTypes_CompleteBitfieldSeq;

#define DDS_XTypes_CompleteBitfieldSeq__alloc() \
((DDS_XTypes_CompleteBitfieldSeq*) dds_alloc (sizeof (DDS_XTypes_CompleteBitfieldSeq)));

#define DDS_XTypes_CompleteBitfieldSeq_allocbuf(l) \
((struct DDS_XTypes_CompleteBitfield *) dds_alloc ((l) * sizeof (struct DDS_XTypes_CompleteBitfield)))
typedef struct DDS_XTypes_MinimalBitfield
{
  struct DDS_XTypes_CommonBitfield common;
  DDS_XTypes_NameHash name_hash;
} DDS_XTypes_MinimalBitfield;

typedef struct DDS_XTypes_MinimalBitfieldSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_MinimalBitfield *_buffer;
  bool _release;
} DDS_XTypes_MinimalBitfieldSeq;

#define DDS_XTypes_MinimalBitfieldSeq__alloc() \
((DDS_XTypes_MinimalBitfieldSeq*) dds_alloc (sizeof (DDS_XTypes_MinimalBitfieldSeq)));

#define DDS_XTypes_MinimalBitfieldSeq_allocbuf(l) \
((struct DDS_XTypes_MinimalBitfield *) dds_alloc ((l) * sizeof (struct DDS_XTypes_MinimalBitfield)))
typedef struct DDS_XTypes_CompleteBitsetHeader
{
  struct DDS_XTypes_CompleteTypeDetail detail;
} DDS_XTypes_CompleteBitsetHeader;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalBitsetHeader
{
} DDS_XTypes_MinimalBitsetHeader;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteBitsetType
{
  DDS_XTypes_BitsetTypeFlag bitset_flags;
  struct DDS_XTypes_CompleteBitsetHeader header;
  DDS_XTypes_CompleteBitfieldSeq field_seq;
} DDS_XTypes_CompleteBitsetType;

typedef struct DDS_XTypes_MinimalBitsetType
{
  DDS_XTypes_BitsetTypeFlag bitset_flags;
  /* struct DDS_XTypes_MinimalBitsetHeader header */ /* no members */
  DDS_XTypes_MinimalBitfieldSeq field_seq;
} DDS_XTypes_MinimalBitsetType;

#if 0 /* empty struct */
typedef struct DDS_XTypes_CompleteExtendedType
{
} DDS_XTypes_CompleteExtendedType;
#endif /* empty struct */

typedef struct DDS_XTypes_CompleteTypeObject
{
  uint8_t _d;
  union
  {
    struct DDS_XTypes_CompleteAliasType alias_type;
    struct DDS_XTypes_CompleteAnnotationType annotation_type;
    struct DDS_XTypes_CompleteStructType struct_type;
    struct DDS_XTypes_CompleteUnionType union_type;
    struct DDS_XTypes_CompleteBitsetType bitset_type;
    struct DDS_XTypes_CompleteSequenceType sequence_type;
    struct DDS_XTypes_CompleteArrayType array_type;
    struct DDS_XTypes_CompleteMapType map_type;
    struct DDS_XTypes_CompleteEnumeratedType enumerated_type;
    struct DDS_XTypes_CompleteBitmaskType bitmask_type;
    /* struct DDS_XTypes_CompleteExtendedType extended_type */ /* no members */
  } _u;
} DDS_XTypes_CompleteTypeObject;

#if 0 /* empty struct */
typedef struct DDS_XTypes_MinimalExtendedType
{
} DDS_XTypes_MinimalExtendedType;
#endif /* empty struct */

typedef struct DDS_XTypes_MinimalTypeObject
{
  uint8_t _d;
  union
  {
    struct DDS_XTypes_MinimalAliasType alias_type;
    struct DDS_XTypes_MinimalAnnotationType annotation_type;
    struct DDS_XTypes_MinimalStructType struct_type;
    struct DDS_XTypes_MinimalUnionType union_type;
    struct DDS_XTypes_MinimalBitsetType bitset_type;
    struct DDS_XTypes_MinimalSequenceType sequence_type;
    struct DDS_XTypes_MinimalArrayType array_type;
    struct DDS_XTypes_MinimalMapType map_type;
    struct DDS_XTypes_MinimalEnumeratedType enumerated_type;
    struct DDS_XTypes_MinimalBitmaskType bitmask_type;
    /* struct DDS_XTypes_MinimalExtendedType extended_type */ /* no members */
  } _u;
} DDS_XTypes_MinimalTypeObject;

typedef struct DDS_XTypes_TypeObject
{
  uint8_t _d;
  union
  {
    struct DDS_XTypes_CompleteTypeObject complete;
    struct DDS_XTypes_MinimalTypeObject minimal;
  } _u;
} DDS_XTypes_TypeObject;

DDS_EXPORT extern const dds_topic_descriptor_t DDS_XTypes_TypeObject_desc;

#define DDS_XTypes_TypeObject__alloc() \
((DDS_XTypes_TypeObject*) dds_alloc (sizeof (DDS_XTypes_TypeObject)));

#define DDS_XTypes_TypeObject_free(d,o) \
dds_sample_free ((d), &DDS_XTypes_TypeObject_desc, (o))

DDS_EXPORT extern const struct dds_cdrstream_desc DDS_XTypes_TypeObject_cdrstream_desc;

typedef struct DDS_XTypes_TypeObjectSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeObject *_buffer;
  bool _release;
} DDS_XTypes_TypeObjectSeq;

#define DDS_XTypes_TypeObjectSeq__alloc() \
((DDS_XTypes_TypeObjectSeq*) dds_alloc (sizeof (DDS_XTypes_TypeObjectSeq)));

#define DDS_XTypes_TypeObjectSeq_allocbuf(l) \
((struct DDS_XTypes_TypeObject *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeObject)))
typedef DDS_XTypes_TypeObjectSeq DDS_XTypes_StronglyConnectedComponent;

#define DDS_XTypes_StronglyConnectedComponent__alloc() \
((DDS_XTypes_StronglyConnectedComponent*) dds_alloc (sizeof (DDS_XTypes_StronglyConnectedComponent)));

typedef struct DDS_XTypes_TypeIdentifierTypeObjectPair
{
  struct DDS_XTypes_TypeIdentifier type_identifier;
  struct DDS_XTypes_TypeObject type_object;
} DDS_XTypes_TypeIdentifierTypeObjectPair;

typedef struct DDS_XTypes_TypeIdentifierTypeObjectPairSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifierTypeObjectPair *_buffer;
  bool _release;
} DDS_XTypes_TypeIdentifierTypeObjectPairSeq;

#define DDS_XTypes_TypeIdentifierTypeObjectPairSeq__alloc() \
((DDS_XTypes_TypeIdentifierTypeObjectPairSeq*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifierTypeObjectPairSeq)));

#define DDS_XTypes_TypeIdentifierTypeObjectPairSeq_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifierTypeObjectPair *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifierTypeObjectPair)))
typedef struct DDS_XTypes_TypeIdentifierPair
{
  struct DDS_XTypes_TypeIdentifier type_identifier1;
  struct DDS_XTypes_TypeIdentifier type_identifier2;
} DDS_XTypes_TypeIdentifierPair;

typedef struct DDS_XTypes_TypeIdentifierPairSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifierPair *_buffer;
  bool _release;
} DDS_XTypes_TypeIdentifierPairSeq;

#define DDS_XTypes_TypeIdentifierPairSeq__alloc() \
((DDS_XTypes_TypeIdentifierPairSeq*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifierPairSeq)));

#define DDS_XTypes_TypeIdentifierPairSeq_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifierPair *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifierPair)))
typedef struct DDS_XTypes_TypeIdentifierWithSize
{
  struct DDS_XTypes_TypeIdentifier type_id;
  uint32_t typeobject_serialized_size;
} DDS_XTypes_TypeIdentifierWithSize;

typedef struct DDS_XTypes_TypeIdentifierWithSizeSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifierWithSize *_buffer;
  bool _release;
} DDS_XTypes_TypeIdentifierWithSizeSeq;

#define DDS_XTypes_TypeIdentifierWithSizeSeq__alloc() \
((DDS_XTypes_TypeIdentifierWithSizeSeq*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifierWithSizeSeq)));

#define DDS_XTypes_TypeIdentifierWithSizeSeq_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifierWithSize *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifierWithSize)))
#ifndef DDS_SEQUENCE_DDS_XTYPES_TYPEIDENTIFIERWITHSIZE_DEFINED
#define DDS_SEQUENCE_DDS_XTYPES_TYPEIDENTIFIERWITHSIZE_DEFINED
typedef struct dds_sequence_DDS_XTypes_TypeIdentifierWithSize
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifierWithSize *_buffer;
  bool _release;
} dds_sequence_DDS_XTypes_TypeIdentifierWithSize;

#define dds_sequence_DDS_XTypes_TypeIdentifierWithSize__alloc() \
((dds_sequence_DDS_XTypes_TypeIdentifierWithSize*) dds_alloc (sizeof (dds_sequence_DDS_XTypes_TypeIdentifierWithSize)));

#define dds_sequence_DDS_XTypes_TypeIdentifierWithSize_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifierWithSize *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifierWithSize)))
#endif /* DDS_SEQUENCE_DDS_XTYPES_TYPEIDENTIFIERWITHSIZE_DEFINED */

typedef struct DDS_XTypes_TypeIdentifierWithDependencies
{
  struct DDS_XTypes_TypeIdentifierWithSize typeid_with_size;
  int32_t dependent_typeid_count;
  dds_sequence_DDS_XTypes_TypeIdentifierWithSize dependent_typeids;
} DDS_XTypes_TypeIdentifierWithDependencies;

typedef struct DDS_XTypes_TypeIdentifierWithDependenciesSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeIdentifierWithDependencies *_buffer;
  bool _release;
} DDS_XTypes_TypeIdentifierWithDependenciesSeq;

#define DDS_XTypes_TypeIdentifierWithDependenciesSeq__alloc() \
((DDS_XTypes_TypeIdentifierWithDependenciesSeq*) dds_alloc (sizeof (DDS_XTypes_TypeIdentifierWithDependenciesSeq)));

#define DDS_XTypes_TypeIdentifierWithDependenciesSeq_allocbuf(l) \
((struct DDS_XTypes_TypeIdentifierWithDependencies *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeIdentifierWithDependencies)))
typedef struct DDS_XTypes_TypeInformation
{
  struct DDS_XTypes_TypeIdentifierWithDependencies minimal;
  struct DDS_XTypes_TypeIdentifierWithDependencies complete;
} DDS_XTypes_TypeInformation;

DDS_EXPORT extern const dds_topic_descriptor_t DDS_XTypes_TypeInformation_desc;

#define DDS_XTypes_TypeInformation__alloc() \
((DDS_XTypes_TypeInformation*) dds_alloc (sizeof (DDS_XTypes_TypeInformation)));

#define DDS_XTypes_TypeInformation_free(d,o) \
dds_sample_free ((d), &DDS_XTypes_TypeInformation_desc, (o))

DDS_EXPORT extern const struct dds_cdrstream_desc DDS_XTypes_TypeInformation_cdrstream_desc;

typedef struct DDS_XTypes_TypeInformationSeq
{
  uint32_t _maximum;
  uint32_t _length;
  struct DDS_XTypes_TypeInformation *_buffer;
  bool _release;
} DDS_XTypes_TypeInformationSeq;

#define DDS_XTypes_TypeInformationSeq__alloc() \
((DDS_XTypes_TypeInformationSeq*) dds_alloc (sizeof (DDS_XTypes_TypeInformationSeq)));

#define DDS_XTypes_TypeInformationSeq_allocbuf(l) \
((struct DDS_XTypes_TypeInformation *) dds_alloc ((l) * sizeof (struct DDS_XTypes_TypeInformation)))
#ifdef __cplusplus
}
#endif

#endif /* DDSI_XT_TYPEINFO_H */

#include <jni.h>
#include <string>
#ifdef _ANDROID_AAR
#include <android/log.h>
#endif
#include "kscom.h"
#include "include/dds/ddsc/dds_public_qosdefs.h"
#include "include/dds/ddsc/dds_public_listener.h"
#include "include/dds/ddsc/dds_basic_types.h"
#include "include/dds/dds.h"
#include "include/dds/ddsrt/time.h"
#include <iostream>

// 假设这些结构体已经在头文件中定义
//typedef int32_t dds_entity_t;
//struct dds_qos;
//typedef struct dds_qos dds_qos_t;
//struct dds_listener;
//typedef struct dds_listener dds_listener_t;
//typedef int int32_t;
//typedef unsigned char uint8_t;
//typedef intptr_t dds_attach_t;
//typedef int64_t dds_duration_t;
//typedef int32_t dds_return_t;
//typedef int64_t dds_time_t;
#define TAG "JNI_LOG"

dds_qos_t *getCppQos(JNIEnv *env, jobject qos);

const char *concatenate_and_return(const char *format, int num) {
    static char result[100];  // 静态数组，确保返回时字符串有效
    sprintf(result, format, num);  // 拼接字符串和数字
    return result;  // 返回 const char *
}

void printLogFromJNI(JNIEnv *env, jobject thiz, const char *message) {
    // 使用 Log 类输出日志
    jclass kscomNativeLibClass = env->FindClass("com/seres/dds/sdk/KScomNativeLib");
    jfieldID instanceFieldId = env->GetStaticFieldID(kscomNativeLibClass, "INSTANCE",
                                                     "Lcom/seres/dds/sdk/KScomNativeLib;");
    jobject kScomNativeLibInstance = env->GetStaticObjectField(kscomNativeLibClass,
                                                               instanceFieldId);
    jmethodID printLogMethodId = env->GetMethodID(kscomNativeLibClass, "printLogFromJNI",
                                                  "(Ljava/lang/String;)V");
    jstring msg = env->NewStringUTF(message);
    env->CallVoidMethod(kScomNativeLibInstance, printLogMethodId, msg);
}

// 映射 C 中的 dds_sample_state_t 到 Kotlin 中的 SampleState
jobject mapSampleStateToKotlinState(JNIEnv *env, int state) {
    jclass kScomNativeLibClass = env->FindClass("com/seres/dds/sdk/KScomNativeLib");
    if (kScomNativeLibClass == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib class");
    }

    jfieldID instanceFieldID = env->GetStaticFieldID(kScomNativeLibClass, "INSTANCE",
                                                     "Lcom/seres/dds/sdk/KScomNativeLib;");
    if (instanceFieldID == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib INSTANCE field");
    }

    jobject kScomNativeLibInstance = env->GetStaticObjectField(kScomNativeLibClass,
                                                               instanceFieldID);
    if (kScomNativeLibInstance == nullptr) {
        return env->NewStringUTF("Failed get KScomNativeLib INSTANCE object");
    }

    jmethodID jMapMethodId = env->GetMethodID(kScomNativeLibClass, "mapIntToSampleState",
                                              "(I)Lcom/seres/dds/sdk/core/SampleState;");
    if (jMapMethodId == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapSampleStateToInt method");
    }

    jobject sampleState = env->CallObjectMethod(kScomNativeLibInstance, jMapMethodId, state);
    if (sampleState == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapSampleStateToInt method");
    }
    return sampleState;
}

jobject mapViewStateToKotlinState(JNIEnv *env, int state) {
    jclass kScomNativeLibClass = env->FindClass("com/seres/dds/sdk/KScomNativeLib");
    if (kScomNativeLibClass == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib class");
    }

    jfieldID instanceFieldID = env->GetStaticFieldID(kScomNativeLibClass, "INSTANCE",
                                                     "Lcom/seres/dds/sdk/KScomNativeLib;");
    if (instanceFieldID == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib INSTANCE field");
    }

    jobject kScomNativeLibInstance = env->GetStaticObjectField(kScomNativeLibClass,
                                                               instanceFieldID);
    if (kScomNativeLibInstance == nullptr) {
        return env->NewStringUTF("Failed get KScomNativeLib INSTANCE object");
    }

    jmethodID jMapMethodId = env->GetMethodID(kScomNativeLibClass, "mapIntToViewState",
                                              "(I)Lcom/seres/dds/sdk/core/ViewState;");
    if (jMapMethodId == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapViewStateToInt method");
    }

    jobject viewState = env->CallObjectMethod(kScomNativeLibInstance, jMapMethodId, state);
    if (viewState == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapViewStateToInt method");
    }
    return viewState;
}

jobject mapInstanceStateToKotlinState(JNIEnv *env, int state) {
    jclass kScomNativeLibClass = env->FindClass("com/seres/dds/sdk/KScomNativeLib");
    if (kScomNativeLibClass == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib class");
    }

    jfieldID instanceFieldID = env->GetStaticFieldID(kScomNativeLibClass, "INSTANCE",
                                                     "Lcom/seres/dds/sdk/KScomNativeLib;");
    if (instanceFieldID == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib INSTANCE field");
    }

    jobject kScomNativeLibInstance = env->GetStaticObjectField(kScomNativeLibClass,
                                                               instanceFieldID);
    if (kScomNativeLibInstance == nullptr) {
        return env->NewStringUTF("Failed get KScomNativeLib INSTANCE object");
    }

    jmethodID jMapMethodId = env->GetMethodID(kScomNativeLibClass, "mapIntToInstanceState",
                                              "(I)Lcom/seres/dds/sdk/core/InstanceState;");
    if (jMapMethodId == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapInstanceStateToInt method");
    }

    jobject instanceState = env->CallObjectMethod(kScomNativeLibInstance, jMapMethodId, state);
    if (instanceState == nullptr) {
        return env->NewStringUTF("Failed find KScomNativeLib mapInstanceStateToInt method");
    }
    return instanceState;
}

// 根据kotlin传入的liveliness_kind 映射到C中的dds_liveliness_kind_t枚举类型
dds_liveliness_kind_t mapKotlinLiveNessKindToCKind(const char *kindEnumName) {
    if (strcmp(kindEnumName, "DDS_LIVELINESS_AUTOMATIC") == 0) {
        return DDS_LIVELINESS_AUTOMATIC;
    } else if (strcmp(kindEnumName, "DDS_LIVELINESS_MANUAL_BY_PARTICIPANT") == 0) {
        return DDS_LIVELINESS_MANUAL_BY_PARTICIPANT;
    } else if (strcmp(kindEnumName, "DDS_LIVELINESS_MANUAL_BY_TOPIC") == 0) {
        return DDS_LIVELINESS_MANUAL_BY_TOPIC;
    } else {
        return DDS_LIVELINESS_AUTOMATIC;
    }
}

// 根据kotlin传入的reliable_kind 映射到C中的dds_reliability_kind枚举类型
dds_reliability_kind mapKotlinReliableKindToCKind(const char* kindEnumName){
    if (strcmp(kindEnumName, "DDS_RELIABILITY_BEST_EFFORT") == 0) {
        return DDS_RELIABILITY_BEST_EFFORT;
    } else if (strcmp(kindEnumName, "DDS_RELIABILITY_RELIABLE") == 0) {
        return DDS_RELIABILITY_RELIABLE;
    } else {
        return DDS_RELIABILITY_RELIABLE;
    }
}

// 根据传入的Kotlin Qos对象转为CPP的dds_qos_t对象
dds_qos_t *getCppQos(JNIEnv *env, jobject qos) {
    dds_qos_t *ddsQos = (dds_qos_t *) malloc(sizeof(dds_qos_t));
    if (qos != NULL) {
        jclass qosClass = env->GetObjectClass(qos);
        jfieldID presentFieldId = env->GetFieldID(qosClass, "present", "J");
        if (presentFieldId != NULL) {
            jlong present = env->GetLongField(qos, presentFieldId);
            ddsQos->present = present;
        }
        jfieldID aliasedFieldId = env->GetFieldID(qosClass, "aliased", "J");
        if (aliasedFieldId != NULL) {
            jlong aliased = env->GetLongField(qos, aliasedFieldId);
            ddsQos->aliased = aliased;
        }
        jfieldID topicNameFieldId = env->GetFieldID(qosClass, "topic_name", "Ljava/lang/String;");
        if (topicNameFieldId != NULL) {
            jstring topicName = (jstring) env->GetObjectField(qos, topicNameFieldId);
            char *topicNameNative = const_cast<char *>(env->GetStringUTFChars(topicName, NULL));
            ddsQos->topic_name = topicNameNative;
        }
        jfieldID typeNameFieldId = env->GetFieldID(qosClass, "type_name", "Ljava/lang/String;");
        if (typeNameFieldId != NULL) {
            jstring typeName = (jstring) env->GetObjectField(qos, typeNameFieldId);
            char *typeNameNative = const_cast<char *>(env->GetStringUTFChars(typeName, NULL));
            ddsQos->type_name = typeNameNative;
        }
        jfieldID entityNameFieldId = env->GetFieldID(qosClass, "entity_name", "Ljava/lang/String;");
        if (entityNameFieldId != NULL) {
            jstring entityName = (jstring) env->GetObjectField(qos, entityNameFieldId);
            char *entityNameNative = const_cast<char *>(env->GetStringUTFChars(entityName, NULL));
            ddsQos->entity_name = entityNameNative;
        }
        jfieldID liveLinessFieldId = env->GetFieldID(qosClass, "liveLiness", "Lcom/seres/dds/sdk/DDSLiveLinessQosPolicy;");
        if (liveLinessFieldId != NULL) {
            jobject liveLinessObj = env->GetObjectField(qos, liveLinessFieldId);
            if (liveLinessObj != NULL) {
                jclass liveLinessQosPolicyClass = env->GetObjectClass(liveLinessObj);
                jfieldID liveLinessQosPolicyKindFieldId = env->GetFieldID(liveLinessQosPolicyClass, "kind", "Lcom/seres/dds/sdk/DDSLiveLinessKind;");
                //获取到liveLiness Kind枚举类型
                jobject liveLinessKindObj = env->GetObjectField(liveLinessObj, liveLinessQosPolicyKindFieldId);
                jclass liveLinessKindClass = env->GetObjectClass(liveLinessKindObj);
                jmethodID liveLinessKindNameMethod = env->GetMethodID(liveLinessKindClass, "name", "()Ljava/lang/String;");
                jstring liveLinessKindNameObj = static_cast<jstring>(env->CallObjectMethod(liveLinessKindObj, liveLinessKindNameMethod));
                ddsQos->liveliness.kind = mapKotlinLiveNessKindToCKind(env->GetStringUTFChars(liveLinessKindNameObj, NULL));
                jfieldID liveLinessDurationFieldId = env->GetFieldID(liveLinessQosPolicyClass, "duration", "J");
                jlong liveLinessDuration = env->GetLongField(liveLinessObj, liveLinessDurationFieldId);
                ddsQos->liveliness.lease_duration = liveLinessDuration;
#ifdef _ANDROID_AAR
                __android_log_print(ANDROID_LOG_INFO, TAG, "dds qos liveLiness lease_duration=%ld", ddsQos->liveliness.lease_duration);
#endif
            }
        }
        jfieldID reliabilityFieldId = env->GetFieldID(qosClass, "reliability", "Lcom/seres/dds/sdk/DDSReliabilityQosPolicy;");
        if (reliabilityFieldId != NULL) {
            jobject reliabilityObj = env->GetObjectField(qos, reliabilityFieldId);
            if (reliabilityObj != NULL) {
                jclass reliabilityQosPolicyClass = env->GetObjectClass(reliabilityObj);
                jfieldID reliabilityKindFieldId = env->GetFieldID(reliabilityQosPolicyClass, "kind", "Lcom/seres/dds/sdk/DDSReliabilityKind;");
                jobject reliabilityKindObj = env->GetObjectField(reliabilityObj, reliabilityKindFieldId);
                jclass reliabilityKindClass = env->GetObjectClass(reliabilityKindObj);
                jmethodID reliabilityKindNameMethodId = env->GetMethodID(reliabilityKindClass, "name", "()Ljava/lang/String;");
                jstring reliabilityKindNameObj = static_cast<jstring>(env->CallObjectMethod(reliabilityKindObj, reliabilityKindNameMethodId));
                ddsQos->reliability.kind = mapKotlinReliableKindToCKind(env->GetStringUTFChars(reliabilityKindNameObj, NULL));
                jfieldID maxBlockingTimeFieldId = env->GetFieldID(reliabilityQosPolicyClass, "maxBlockingTime", "J");
                jlong maxBlockingTimeObj = env->GetLongField(reliabilityObj, maxBlockingTimeFieldId);
                ddsQos->reliability.max_blocking_time = maxBlockingTimeObj;
#ifdef _ANDROID_AAR
                __android_log_print(ANDROID_LOG_INFO, TAG, "dds qos reliability max_blocking_time=%ld", ddsQos->reliability.max_blocking_time);
#endif
            }
        }
    }
    return ddsQos;
}


/**
 * dds_entity_t kscom_create_participant(const dds_domainid_t domain, const dds_qos_t *qos, const dds_listener_t *listener);
 * typedef unsigned uint32_t;
 * typedef uint32_t dds_domainid_t;
 * struct dds_qos;
 * typedef struct dds_qos dds_qos_t;
 * struct dds_listener;
 * typedef struct dds_listener dds_listener_t;
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateParticipant(
        JNIEnv *env,
        jobject /* this */,
        jint domain,
        jlong qos,
        jlong listener) {
    const dds_qos_t *qosPrt = reinterpret_cast<const dds_qos_t *>(qos);
    const dds_listener_t *listenerPtr = reinterpret_cast<const dds_listener_t *>(listener);
    jint result = kscom_create_participant(domain, nullptr, nullptr);
#ifdef _ANDROID_AAR
    __android_log_print(ANDROID_LOG_INFO, TAG, "kScomCreateParticipant result=%d", result);
#endif
    return static_cast<jint>(result);
}

/**
 * dds_entity_t kscom_create_publisher(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener);
 * 1.typedef int int32_t;
 * 2.typedef int32_t dds_entity_t;
 * 3.struct dds_qos;
 * 4.typedef struct dds_qos dds_qos_t;
 * 5.struct dds_listener;
 * 6.typedef struct dds_listener dds_listener_t;
*/
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreatePublisher(
        JNIEnv *env,
        jobject /* this */,
        jint participant,
        jlong qos,
        jlong listener) {
    const dds_qos_t *qosPrt = reinterpret_cast<const dds_qos_t *>(qos);
    const dds_listener_t *listenerPtr = reinterpret_cast<const dds_listener_t *>(listener);
    dds_entity_t result = kscom_create_publisher(participant, qosPrt, listenerPtr);
    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_suspend(dds_entity_t publisher);
 *
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomSuspend(
        JNIEnv *env,
        jobject /* this */,
        jint publisher) {
    dds_entity_t result = kscom_suspend(publisher);
    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_resume(dds_entity_t publisher);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomResume(
        JNIEnv *env,
        jobject /* this */,
        jint publisher) {
    dds_entity_t result = kscom_resume(publisher);
    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_wait_for_acks(dds_entity_t publisher_or_writer, dds_duration_t timeout);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitForAcks(
        JNIEnv *env,
        jobject /* this */,
        jint publisherOrWriter,
        jint timeout) {
    dds_entity_t result = kscom_wait_for_acks(publisherOrWriter, timeout);
    return static_cast<jint>(result);
}

/**
 * dds_entity_t kscom_create_writer(dds_entity_t participant_or_publisher, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateWriter(
        JNIEnv *env,
        jobject /* this */,
        jint participantOrPublisher,
        jint topic,
        jobject qos,
        jobject listener) {
//    dds_qos_t *ddsQos = getCppQos(env, qos);
    jint result = kscom_create_writer(participantOrPublisher, topic, nullptr,
                                      reinterpret_cast<const dds_listener_t *>(listener));
    return static_cast<jint>(result);
}

/**
 * dds_entity_t kscom_create_subscriber(dds_entity_t participant, const dds_qos_t *qos, const dds_listener_t *listener);
 */
// TODO
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateSubscriber(
        JNIEnv *env,
        jobject /* this */,
        jint participant,
        jlong qos,
        jlong listener) {

    jint result = kscom_create_subscriber(participant, reinterpret_cast<const dds_qos_t *>(qos),
                                          reinterpret_cast<const dds_listener_t *>(listener));
    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_notify_readers(dds_entity_t subscriber);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomNotifyReaders(
        JNIEnv *env,
        jobject /* this */,
        jint subscriber) {
    dds_entity_t result = kscom_notify_readers(subscriber);
    return static_cast<jint>(result);
}

/**
 * dds_entity_t kscom_create_reader(dds_entity_t participant_or_subscriber, dds_entity_t topic, const dds_qos_t *qos, const dds_listener_t *listener);
 */
//TODO
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateReader(
        JNIEnv *env,
        jobject /* this */,
        jint participantOrSubscriber,
        jint topic,
        jlong qos,
        jlong listener) {


    dds_entity_t result = kscom_create_reader(participantOrSubscriber, topic, nullptr,
                                              reinterpret_cast<const dds_listener_t *>(listener));
    return static_cast<jint>(result);
}

/**
 * dds_entity_t kscom_create_topic(dds_entity_t participant, const char* topicname, idl_type_desc_t* desc, const dds_qos_t *qos, const dds_listener_t *listener);
 */
//// TODO
//extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateTopic(
//        JNIEnv *env,
//        jobject /* this */,
//        jint participant,
//        jstring topicName,
//        jlong desc,
//        jlong qos,
//        jlong listener) {
//    // 将 jstring 转换为 C 字符串
//    const char *cTopicName = env->GetStringUTFChars(topicName, nullptr);
//    if (cTopicName == nullptr) {
//        // 处理异常
//        return -1;
//    }
//    // 将 jlong 转换为指针类型
//    const idl_type_desc_t *descPtr = reinterpret_cast<const idl_type_desc_t *>(desc);
//    const dds_qos_t *qosPtr = reinterpret_cast<const dds_qos_t *>(qos);
//    const dds_listener_t *listenerPtr = reinterpret_cast<const dds_listener_t *>(listener);
//    // 调用 C++ 函数
//    jint result = kscom_create_topic(participant, cTopicName, descPtr, qosPtr, listenerPtr);
//    // 释放 C 字符串
//    env->ReleaseStringUTFChars(topicName, cTopicName);
//    // 返回结果
//    return static_cast<jint>(result);
//}

/**
 * dds_entity_t kscom_create_waitset(dds_entity_t owner);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateWaitSet(
        JNIEnv *env,
        jobject /* this */,
        jint owner) {
    jint result = kscom_create_waitset(owner);
    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_waitset_attach(dds_entity_t waitset, dds_entity_t entity, dds_attach_t x);
 *
 * typedef int intptr_t;
 * typedef intptr_t dds_attach_t;
 */
extern "C" JNIEXPORT jobject JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitSetAttach(
        JNIEnv *env,
        jobject /* this */,
        jint waitSet,
        jint entity) {
    int *value_ptr = (int*)malloc(sizeof(int));
    dds_entity_t result = kscom_waitset_attach(waitSet, entity, (dds_attach_t )value_ptr);

    jclass getWaitSetAttachRespClass = env->FindClass("com/seres/dds/sdk/idl/WaitSetAttachResp");
    if(getWaitSetAttachRespClass == nullptr){
        return nullptr;
    }
    jmethodID constructorId = env->GetMethodID(getWaitSetAttachRespClass, "<init>","(IJ)V");
    if(constructorId == nullptr){
        return nullptr;
    }
    jobject getWaitSetAttachRespObject = env->NewObject(getWaitSetAttachRespClass, constructorId, result, (jlong)value_ptr);
    env->DeleteLocalRef(getWaitSetAttachRespClass);
    return getWaitSetAttachRespObject;
}

/**
 * dds_return_t kscom_waitset_detach(dds_entity_t waitset, dds_entity_t entity);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitSetDetach(
        JNIEnv *env,
        jobject /* this */,
        jint waitSet,
        jint entity,
        jlong valuePtr) {
    dds_entity_t result = kscom_waitset_detach(waitSet, entity);
    free((int*)valuePtr);


    return result;
//    return static_cast<jint>(result);
}

/**
 * dds_return_t kscom_waitset_wait(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_duration_t reltimeout);
 *
 * typedef unsigned int size_t;
 * typedef long long int64_t;
 * typedef int64_t dds_duration_t;
 * typedef int32_t dds_return_t;
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitSetWait(
        JNIEnv *env,
        jobject /* this */,
        jint waitSet,
        jlong nxs,
        jlong relTimeout){

    dds_attach_t xs;
    dds_attach_t *xs_ptr = &xs;
    if(nxs == 0){
        xs_ptr  = NULL;
    }
    jint result = kscom_waitset_wait(waitSet,xs_ptr, (unsigned long)nxs,relTimeout);
    return result;
}

/**
 * dds_return_t kscom_waitset_wait_until(dds_entity_t waitset, dds_attach_t *xs, size_t nxs, dds_time_t abstimeout);
 * typedef long long int64_t;
 * typedef int64_t dds_time_t;
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitSetWaitUntil(
        JNIEnv *env,
        jobject /* this */,
        jint waitSet,
        jlong nxs,
        jlong absTimeout) {
    // 将 jlong 转换为指针类型
//    dds_attach_t *xsPtr = reinterpret_cast<dds_attach_t *>(xs);
//    dds_attach_t  xs_ptr;
    dds_attach_t* xs_ptr = (dds_attach_t*)malloc(sizeof(dds_attach_t)*nxs);

    jint result = kscom_waitset_wait_until(waitSet, xs_ptr,
                                           (unsigned long)nxs,
                                           absTimeout);
    free(xs_ptr);
    return result;
}

/**
 * dds_return_t kscom_waitset_set_trigger(dds_entity_t waitset, bool trigger);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomWaitSetSetTrigger(
        JNIEnv *env,
        jobject /* this */,
        jint waitSet,
        jboolean trigger) {
    jint result = kscom_waitset_set_trigger(static_cast<dds_entity_t>(waitSet), trigger);
    return static_cast<jint>(result);
}



/**
 * samples* kscom_take(dds_entity_t reader_or_condition, long long N);
 *
// sample_structs.h

#ifndef SAMPLE_STRUCTS_H
#define SAMPLE_STRUCTS_H

#include <stdint.h>
#include <stdbool.h>

// 类型定义和结构体
typedef unsigned int size_t;
typedef int64_t dds_time_t;
typedef uint64_t dds_instance_handle_t;
typedef unsigned uint32_t;

// kscom_sample_container 结构体
typedef struct kscom_sample_container {
    void* usample;  // usample 是一个指针，指向样本数据
    size_t usample_size;  // usample 的大小
} kscom_sample_container_t;

// 状态枚举
typedef enum dds_sample_state {
    DDS_SST_READ = 1u,
    DDS_SST_NOT_READ = 2u
} dds_sample_state_t;

typedef enum dds_view_state {
    DDS_VST_NEW = 4u,
    DDS_VST_OLD = 8u
} dds_view_state_t;

typedef enum dds_instance_state {
    DDS_IST_ALIVE = 16u,
    DDS_IST_NOT_ALIVE_DISPOSED = 32u,
    DDS_IST_NOT_ALIVE_NO_WRITERS = 64u
} dds_instance_state_t;

// dds_sample_info 结构体
typedef struct dds_sample_info {
    dds_sample_state_t sample_state;
    dds_view_state_t view_state;
    dds_instance_state_t instance_state;
    bool valid_data;
    dds_time_t source_timestamp;
    dds_instance_handle_t instance_handle;
    dds_instance_handle_t publication_handle;
    uint32_t disposed_generation_count;
    uint32_t no_writers_generation_count;
    uint32_t sample_rank;
    uint32_t generation_rank;
    uint32_t absolute_generation_rank;
} dds_sample_info_t;

// sample 结构体
typedef struct sample {
    kscom_sample_container_t container;
    dds_sample_info_t info;
} sample;

// samples 结构体
typedef struct samples {
    sample* kdata;
    long long ncount;
} samples;

 */
extern "C" {
JNIEXPORT jlong JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomTake(
        JNIEnv *env,
        jobject /* this */,
        jint readerOrCondition, jlong N) {
    // 调用 C++ 函数
    samples *result = kscom_take(readerOrCondition, N);
    // 返回 samples* 的指针
    return reinterpret_cast<jlong>(result);
}
JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getKdata(JNIEnv *env, jobject /* this */,
                                               jlong samplesPointer) {
    // 获取 samples 结构体中的 kdata 指针
    samples *result = reinterpret_cast<samples *>(samplesPointer);
    return reinterpret_cast<jlong>(result->kdata);
}

JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getSample(JNIEnv *env, jobject /* this */, jlong kdataPointer,
                                                jlong index) {
    // 获取 kdata 中的具体 sample
    sample *kdata = reinterpret_cast<sample *>(kdataPointer);
    return reinterpret_cast<jlong>(&kdata[index]);
}

JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getContainerUsampleSize(JNIEnv *env, jobject /* this */,
                                                              jlong samplePointer) {
    // 获取 sample 结构体中的 container.usample_size
    sample *s = reinterpret_cast<sample *>(samplePointer);
    return static_cast<jint>(s->container.usample_size);
}

JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getContainerUsample(JNIEnv *env, jobject /* this */,
                                                          jlong samplePointer) {
    // 获取 sample 结构体中的 container.usample
    sample *s = reinterpret_cast<sample *>(samplePointer);
    return reinterpret_cast<jlong>(s->container.usample);
}

JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getSampleState(JNIEnv *env, jobject /* this */,
                                                     jlong samplePointer) {
    // 获取 sample 结构体中的 info.sample_state
    sample *s = reinterpret_cast<sample *>(samplePointer);
    return static_cast<jint>(s->info.sample_state);
}

JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_getNCount(JNIEnv *env, jobject /* this */,
                                                jlong samples_pointer) {
    samples *result = reinterpret_cast<samples *>(samples_pointer);
    return static_cast<jlong>(result->ncount);
}
}

/**
 * dds_return_t kscom_get_mask(dds_entity_t condition, uint32_t *mask);
 */

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetMask(JNIEnv *env, jobject thiz, jint condition) {
    // TODO: implement kScomGetMask()
    uint32_t mask = 0;
    int ret = kscom_get_mask(condition,&mask);
    jclass getMaskRespClass = env->FindClass("com/seres/dds/sdk/idl/GetMaskResp");
    if(getMaskRespClass == nullptr){
        return nullptr;
    }
    jmethodID constructorId = env->GetMethodID(getMaskRespClass, "<init>","(II)V");
    if(constructorId == nullptr){
        return nullptr;
    }
    jobject getMaskRespObject = env->NewObject(getMaskRespClass, constructorId, ret, (jint)mask);
    env->DeleteLocalRef(getMaskRespClass);
    return getMaskRespObject;
}

/**
* dds_return_t kscom_triggered(dds_entity_t entity);
*/
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomTriggered(
        JNIEnv *env,
        jobject /*this*/,
        jint entity_id) {
    jint result = kscom_triggered(entity_id);
    return result;
}

/**
 * dds_entity_t kscom_create_guardcondition(dds_entity_t owner);
 */
extern "C" JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateGuardCondition(
        JNIEnv *env,
        jobject /*this*/,
        jint owner) {
    jint result = kscom_create_guardcondition(owner);
    return result;
}

/**
 * dds_return_t kscom_set_guardcondition(dds_entity_t guardcond, bool triggered);
 */
extern "C" JNIEXPORT jint JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomSetGuardCondition(
        JNIEnv *env,
        jobject /*this*/,
        jint guardcond,
        jboolean triggered) {
    jint result = kscom_set_guardcondition(guardcond, triggered);
    return result;
}

/**
 * dds_return_t kscom_read_guardcondition(dds_entity_t guardcond, bool *triggered);
 */
extern "C" JNIEXPORT jobject JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomReadGuardCondition(
        JNIEnv *env,
        jobject /*this*/,
        jint guardcond) {
    bool triggered = false;
    int ret = kscom_read_guardcondition(guardcond,&triggered);
    jclass getReadGuardConditionResp = env->FindClass("com/seres/dds/sdk/idl/ReadGuardConditionResp");
    if(getReadGuardConditionResp == nullptr){
        return nullptr;
    }
    jmethodID constructorId = env->GetMethodID(getReadGuardConditionResp, "<init>","(IZ)V");
    if(constructorId == nullptr){
        return nullptr;
    }
    jobject getReadGuardConditionRespObject = env->NewObject(getReadGuardConditionResp, constructorId, ret, (jint)triggered);
    env->DeleteLocalRef(getReadGuardConditionResp);
    return getReadGuardConditionRespObject;
}

/**
 * dds_return_t kscom_take_guardcondition(dds_entity_t guardcond, bool *triggered);
 */
extern "C" JNIEXPORT jobject JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomTakeGuardCondition(
        JNIEnv *env,
        jobject /*this*/,
        jint guardcond) {
    bool triggered = false;
    int ret = kscom_take_guardcondition(guardcond,&triggered);
    jclass getTakeGuardConditionResp = env->FindClass("com/seres/dds/sdk/idl/TakeGuardConditionResp");
    if(getTakeGuardConditionResp == nullptr){
        return nullptr;
    }
    jmethodID constructorId = env->GetMethodID(getTakeGuardConditionResp, "<init>","(IZ)V");
    if(constructorId == nullptr){
        return nullptr;
    }
    jobject getTakeGuardConditionRespObject = env->NewObject(getTakeGuardConditionResp, constructorId, ret, (jint)triggered);
    env->DeleteLocalRef(getTakeGuardConditionResp);
    return getTakeGuardConditionRespObject;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_KScomDDSWrite(JNIEnv *env, jobject thiz, jint writer,
                                                    jobject container) {
    jclass clazz = env->GetObjectClass(container);
//    jfieldID fieldid1 = env->GetFieldID(clazz, "usample",)/
    kscom_sample_container_t *data = (kscom_sample_container_t *) malloc(
            sizeof(kscom_sample_container_t));
//    jbyteArray bytearray = (jbyteArray)(*env).GetObjectField(container, "usample");
    jfieldID fieldid1 = env->GetFieldID(clazz, "usample", "[B");
    jbyteArray usample = (jbyteArray) env->GetObjectField(container, fieldid1);
    jboolean isCopy;
    jbyte *usample_ptr = env->GetByteArrayElements(usample, &isCopy);


    jfieldID jfieldId2 = env->GetFieldID(clazz, "usample_size", "J");
    data->usample_size = (*env).GetLongField(container, jfieldId2);
    data->usample = usample_ptr;
    return kscom_dds_write(writer, data);
    // TODO: implement KScomDDSWrite()
}


extern "C"
JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateTopic(
        JNIEnv *env, jobject thiz, jint participant, jstring topic_name, jobject desc, jlong qos,
        jlong listener) {
    idl_type_desc_t *desc1 = (idl_type_desc_t *) malloc(sizeof(idl_type_desc_t));
//    char* nativestring = (*env)->Get
//    desc1->keyless = desc.
    jclass clazz = (*env).GetObjectClass(desc);
    if (clazz == NULL) {
        return -1;
    }
    jfieldID fieldid1 = env->GetFieldID(clazz, "typename", "Ljava/lang/String;");
    if (fieldid1 == NULL) {
        return -1;
    }
    jstring typename1 = (jstring) env->GetObjectField(desc, fieldid1);

    char *nativeString = const_cast<char *>(env->GetStringUTFChars(typename1, NULL));
    if (nativeString == NULL) {
        return -1;
    }
    desc1->type_name = nativeString;
    jfieldID fieldid2 = env->GetFieldID(clazz, "keyless", "Z");
    desc1->keyless = env->GetBooleanField(desc, fieldid2);
    jfieldID fieldid3 = env->GetFieldID(clazz, "version_support", "I");
    desc1->version_support = env->GetIntField(desc, fieldid3);
    desc1->xt_type_data = NULL;

    int ret = kscom_create_topic(participant, env->GetStringUTFChars(topic_name, NULL), desc1,
                                 (dds_qos_t *) qos, (dds_listener_t *) qos);

    // env->ReleaseStringUTFChars(typename1, nativeString);

    // TODO: implement kScomCreateTopic()
    return ret;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_KScomDDSTake(JNIEnv *env, jobject thiz, jint reader,
                                                   jlong n) {
    samples *mysamples = kscom_take(reader, n);
//    __android_log_print(ANDROID_LOG_INFO, TAG, "KScomDDSTake mysamples size=%lld",
//                        mysamples->ncount);

    if (mysamples->ncount < 0) {
        return NULL;
    }
    jclass arrayListClass = env->FindClass("java/util/ArrayList");
    if (arrayListClass == NULL) {
        return NULL; // 处理错误
    }
    jmethodID arrayListConstructor = env->GetMethodID(arrayListClass, "<init>", "()V");
    if (arrayListConstructor == NULL) {
        return NULL; // 处理错误
    }
    jobject sampleContainerAndInfoPairArrayList = env->NewObject(arrayListClass, arrayListConstructor);
    if (sampleContainerAndInfoPairArrayList == NULL) {
        return NULL; // 处理错误
    }
    jmethodID addMethod = env->GetMethodID(arrayListClass, "add", "(Ljava/lang/Object;)Z");
    if (addMethod == NULL) {
        return NULL; // 处理错误
    }

    // 获取Pair
    jclass pairClass = env->FindClass("kotlin/Pair");
    if (pairClass == NULL) {
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_ERROR, TAG, "cannot find kotlin pair class");
#endif
        return NULL;
    }
    jmethodID pairConstructorMethodId = env->GetMethodID(pairClass, "<init>", "(Ljava/lang/Object;Ljava/lang/Object;)V");
    if (pairConstructorMethodId == NULL) {
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_ERROR, TAG, "cannot find kotlin pair constructor methodId");
#endif
        return NULL;
    }

    jclass sampleContainerClass = env->FindClass("com/seres/dds/sdk/SampleContainerT");
    if (sampleContainerClass == NULL) {
        return NULL; // 处理错误
    }

    jmethodID sampleContainerConstructor = env->GetMethodID(sampleContainerClass, "<init>", "()V");
    if (sampleContainerConstructor == NULL) {
        return NULL; // 处理错误
    }

    //获取SampleInfo
    jclass sampleInfoClass = env->FindClass("com/seres/dds/sdk/SampleInfo");
    if (sampleInfoClass == NULL) {
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_ERROR, TAG, "cannot find kotlin SampleInfo class");
#endif
        return NULL;
    }
    jmethodID sampleInfoConstructor = env->GetMethodID(sampleInfoClass, "<init>", "()V");
    if (sampleInfoConstructor == NULL) {
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_ERROR, TAG, "cannot find kotlin SampleInfo constructor");
#endif
        return NULL;
    }

    int sample_count = mysamples->ncount;
    sample *kdata = mysamples->kdata;
    int i = 0;
    jfieldID fid_usample = env->GetFieldID(sampleContainerClass, "usample", "[B");
    jfieldID fid_usample_size = env->GetFieldID(sampleContainerClass, "usample_size", "J");

    //获取SampleInfo相关属性id
    jfieldID fieldIdValidData = env->GetFieldID(sampleInfoClass, "valid_data", "Z");
    jfieldID fieldIdSampleState = env->GetFieldID(sampleInfoClass, "sampleState", "Lcom/seres/dds/sdk/core/SampleState;");
    jfieldID fieldIdViewState = env->GetFieldID(sampleInfoClass, "viewState", "Lcom/seres/dds/sdk/core/ViewState;");
    jfieldID fieldIdInstanceState = env->GetFieldID(sampleInfoClass, "instanceState", "Lcom/seres/dds/sdk/core/InstanceState;");
    jfieldID fieldIdSourceTimestamp = env->GetFieldID(sampleInfoClass, "sourceTimestamp", "J");
    jfieldID fieldIdInstanceHandle = env->GetFieldID(sampleInfoClass, "instanceHandle", "I");
    jfieldID fieldIdPublicationHandle = env->GetFieldID(sampleInfoClass, "publicationHandle", "I");
    jfieldID fieldIdDisposedGenerationCount = env->GetFieldID(sampleInfoClass, "disposedGenerationCount", "I");
    jfieldID fieldIdNoWritersGenerationCount = env->GetFieldID(sampleInfoClass, "noWritersGenerationCount", "I");
    jfieldID fieldIdSampleRank = env->GetFieldID(sampleInfoClass, "sampleRank", "I");
    jfieldID fieldIdGenerationRank = env->GetFieldID(sampleInfoClass, "generationRank", "I");
    jfieldID fieldIdAbsoluteGenerationRank = env->GetFieldID(sampleInfoClass, "absoluteGenerationRank", "I");
    while (sample_count) {

        jobject sampleContainerObject = env->NewObject(sampleContainerClass,
                                                       sampleContainerConstructor);
        if (sampleContainerObject == NULL) {
            return NULL; // 处理错误
        }

        jobject sampleInfoObject = env->NewObject(sampleInfoClass, sampleInfoConstructor);
        if (sampleInfoObject == NULL) {
#ifdef _ANDROID_AAR
            __android_log_print(ANDROID_LOG_ERROR, TAG, "failed new SampleInfo object");
#endif
            return NULL;
        }


        int len = kdata[i].container.usample_size;
        jbyte *samplebuffer = static_cast<jbyte *>(kdata[i].container.usample);
        jbyteArray usampleArray = env->NewByteArray(len);
        if (usampleArray == NULL) {
            return NULL; // 处理错误
        }
        env->SetByteArrayRegion(usampleArray, 0, len, (jbyte *) samplebuffer);
        env->SetObjectField(sampleContainerObject, fid_usample, usampleArray);
        env->SetLongField(sampleContainerObject, fid_usample_size, (jlong) len);

        //处理SampleInfo
        dds_sample_info info = kdata[i].info;
        env->SetBooleanField(sampleInfoObject, fieldIdValidData, info.valid_data);
        env->SetObjectField(sampleInfoObject, fieldIdSampleState, mapSampleStateToKotlinState(env, info.sample_state));
        env->SetObjectField(sampleInfoObject, fieldIdViewState, mapViewStateToKotlinState(env, info.view_state));
        env->SetObjectField(sampleInfoObject, fieldIdInstanceState, mapInstanceStateToKotlinState(env, info.instance_state));
        env->SetLongField(sampleInfoObject, fieldIdSourceTimestamp, info.source_timestamp);
        env->SetIntField(sampleInfoObject, fieldIdInstanceHandle, (jint) info.instance_handle);
        env->SetIntField(sampleInfoObject, fieldIdPublicationHandle, (jint) info.publication_handle);
        env->SetIntField(sampleInfoObject, fieldIdDisposedGenerationCount, (jint) info.disposed_generation_count);
        env->SetIntField(sampleInfoObject, fieldIdNoWritersGenerationCount, (jint) info.no_writers_generation_count);
        env->SetIntField(sampleInfoObject, fieldIdSampleRank, (jint) info.sample_rank);
        env->SetIntField(sampleInfoObject, fieldIdGenerationRank, (jint) info.generation_rank);
        env->SetIntField(sampleInfoObject, fieldIdAbsoluteGenerationRank, (jint) info.absolute_generation_rank);

        // 将SampleContainerT对象和SampleInfo对象添加到Pair中
        jobject pairObject = env->NewObject(pairClass, pairConstructorMethodId, sampleContainerObject, sampleInfoObject);

        // 将SampleContainerT对象添加到ArrayList
        env->CallBooleanMethod(sampleContainerAndInfoPairArrayList, addMethod, pairObject);

        // 删除局部引用
        env->DeleteLocalRef(usampleArray);
        env->DeleteLocalRef(sampleContainerObject);
        env->DeleteLocalRef(sampleInfoObject);
        env->DeleteLocalRef(pairObject);


        i++;
        sample_count--;
    }
    return sampleContainerAndInfoPairArrayList;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetStatusMask(JNIEnv *env, jobject thiz, jint entity,
                                                         jlong mask) {
    // TODO: implement kScomSetStatusMask()
    printf("mask = %d", (int32_t) mask);
    return kscom_set_status_mask(entity, (int32_t) mask);
}
extern "C"
JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetStatueMask(JNIEnv *env, jobject thiz, jint entity) {
    // TODO: implement kScomGetStatueMask()
    uint32_t p;
    kscom_get_status_changes(entity, &p);
    return (long) p;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateReadCondition(JNIEnv *env, jobject thiz, jint ref,
                                                               jint mask) {
    int ret = kscom_create_readcondition(ref, mask);
    return ret;
}
extern "C"
JNIEXPORT jint JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetStatusChanges(JNIEnv *env, jobject thiz, jint entity) {
    uint32_t statusPtr;
    int ret = dds_get_status_changes(entity, &statusPtr);
#ifdef _ANDROID_AAR
    __android_log_print(ANDROID_LOG_INFO, TAG, "Get Status changes status ret=%d, status value=%d", ret, statusPtr);
#endif
    int result;
    if (ret == 0) {
        result = statusPtr;
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_INFO, TAG, "Get status changes success, status=%d", statusPtr);
#endif
    } else {
#ifdef _ANDROID_AAR
        __android_log_print(ANDROID_LOG_WARN, TAG, "Get status changes failed, status=%d", statusPtr);
#endif
        result = 0;
    }
    return result;
}

//extern "C"
//JNIEXPORT jobject JNICALL
//Java_com_seres_dds_sdk_KScomNativeLib_DDSCreateQos(JNIEnv *env, jobject thiz) {
//
//}

extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetGuid(JNIEnv *env, jobject thiz, jint ref) {
    dds_guid_t guid;
    dds_get_guid(ref, &guid);
    const int guid_array_len = 16;
    jbyteArray byteArray = env->NewByteArray(guid_array_len);
    if (byteArray == NULL) {
        return NULL;
    }
    jbyte *elements = env->GetByteArrayElements(byteArray, NULL);
    if (elements == NULL) {
        return NULL;
    }
    for (int i = 0; i < guid_array_len; ++i) {
        elements[i] = static_cast<jbyte>(guid.v[i]);
    }
    env->ReleaseByteArrayElements(byteArray, elements, 0);
    return byteArray;
}

extern "C"
JNIEXPORT jlong JNICALL Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateListener(
        JNIEnv *env,
        jobject /* this */) {

    return kscom_create_listener();
}

JavaVM* g_jvm = nullptr;

jobject g_ReaderListenerObj = nullptr;
jobject g_WriterListenerObj = nullptr;

jclass g_SubscriptionMatchedStatus = nullptr;
jclass g_PublicationMatchedStatus = nullptr;

JNIEnv* getJNIEnv() {
    JNIEnv* env;
    jint result = g_jvm->GetEnv((void**)&env, JNI_VERSION_1_6);
    if (result == JNI_OK) {
        return env;
    } else if (result == JNI_EDETACHED) {
        // 如果当前线程未附加到 JVM，附加线程
#ifdef LinuxVersion
        g_jvm->AttachCurrentThread((void**)&env, nullptr);
#else
        g_jvm->AttachCurrentThread(&env, nullptr);
#endif
        return env;
    } else {
        std::cerr << "Failed to get JNIEnv" << std::endl;
        return nullptr;
    }
}

void on_subscription_matched_wrapper(dds_entity_t reader, const dds_subscription_matched_status_t status, void* arg) {

    JNIEnv* env = getJNIEnv();

    if (env == nullptr) {
        std::cout << "Failed to get JNIEnv*" << std::endl;
        abort();
    }

    jobject localListenerObj = env->NewLocalRef(g_ReaderListenerObj);
    if (localListenerObj == nullptr) {
        std::cout << "Failed to create local reference for g_listenerObj" << std::endl;
        abort();
    }

    jclass listenerClass = env->GetObjectClass(localListenerObj);
    if (listenerClass == nullptr) {
        std::cout << "listenerClass is nullptr" << std::endl;
        abort();
    }

    jmethodID methodId = env->GetMethodID(listenerClass, "on_subscription_matched", "(ILcom/seres/dds/sdk/core/SubscriptionMatchedStatus;)V");
    if (methodId == nullptr) {
        std::cout << "methodId is nullptr" << std::endl;
        abort();
    }

    jclass statusClass = g_SubscriptionMatchedStatus;
    if (statusClass == nullptr){
        std::cout << "Failed to get statusClass" << std::endl;
        abort();
    }

    jmethodID constructorId = env->GetMethodID(statusClass, "<init>", "(IIIIJ)V");
    if (constructorId == nullptr){
        std::cout << "Failed to get constructorId" << std::endl;
        jthrowable exc = env->ExceptionOccurred();
        if (exc) {
            env->ExceptionDescribe();
            env->ExceptionClear();
        }
        abort();
    }

    jobject statusObj = env->NewObject(statusClass, constructorId,
                                       static_cast<jint>(status.total_count),
                                       static_cast<jint>(status.total_count_change),
                                       static_cast<jint>(status.current_count),
                                       static_cast<jint>(status.current_count_change),
                                       static_cast<jlong>(status.last_publication_handle));


    env->CallVoidMethod(localListenerObj, methodId, static_cast<jint>(reader), statusObj);
    env->DeleteLocalRef(localListenerObj);
}

extern "C" JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomLsetSubscriptionMatched(
        JNIEnv* env,
        jobject /* this */,
        jlong listenerid,
        jobject Listener,
        jint kind) {

    if (g_ReaderListenerObj == nullptr) {
        g_ReaderListenerObj = env->NewGlobalRef(Listener);
        if (g_ReaderListenerObj == nullptr) {
            std::cout << "Failed to create global reference for Listener" << std::endl;
            abort();
        }
    }

    if (g_jvm == nullptr) {
        if (env->GetJavaVM(&g_jvm) != 0) {
            std::cout << "Failed to get JavaVM" << std::endl;
            abort();
        }

        if (g_jvm == nullptr) {
            std::cout << "JavaVM is nullptr" << std::endl;
            abort();
        }
    }

    g_SubscriptionMatchedStatus = (jclass) env->NewGlobalRef(env->FindClass("com/seres/dds/sdk/core/SubscriptionMatchedStatus"));
    if (g_SubscriptionMatchedStatus == nullptr) {
        std::cout << "Failed to create global reference for SubscriptionMatchedStatus" << std::endl;
        abort();
    }

    dds_lset_subscription_matched(reinterpret_cast<dds_listener_t*>(listenerid), on_subscription_matched_wrapper);
}

void dds_lset_data_available_wrapper(dds_entity_t reader, void* arg) {

    JNIEnv* env = getJNIEnv();
    if (env == nullptr) {
        std::cout << "Failed to get JNIEnv*" << std::endl;
        abort();
    }

    jobject localListenerObj = env->NewLocalRef(g_ReaderListenerObj);
    if (localListenerObj == nullptr) {
        std::cout << "Failed to create local reference for g_listenerObj" << std::endl;
        abort();
    }

    jclass listenerClass = env->GetObjectClass(localListenerObj);
    if (listenerClass == nullptr) {
        std::cout << "listenerClass is nullptr" << std::endl;
        abort();
    }

    jmethodID methodId = env->GetMethodID(listenerClass, "on_data_available", "(I)V");
    if (methodId == nullptr) {
        std::cout << "methodId is nullptr" << std::endl;
        abort();
    }
    env->CallVoidMethod(localListenerObj, methodId, static_cast<jint>(reader));
    env->DeleteLocalRef(localListenerObj);
}

extern "C" JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomLsetOnDataAvailable(
        JNIEnv* env,
        jobject /* this */,
        jlong listenerid,
        jobject Listener,
        jint kind) {

    if (g_ReaderListenerObj == nullptr) {
        g_ReaderListenerObj = env->NewGlobalRef(Listener);
        if (g_ReaderListenerObj == nullptr) {
            std::cout << "Failed to create global reference for Listener" << std::endl;
            abort();
        }
    }

    if (g_jvm == nullptr) {
        if (env->GetJavaVM(&g_jvm) != 0) {
            std::cout << "Failed to get JavaVM" << std::endl;
            abort();
        }

        if (g_jvm == nullptr) {
            std::cout << "JavaVM is nullptr" << std::endl;
            abort();
        }
    }

    dds_lset_data_available(reinterpret_cast<dds_listener_t*>(listenerid), dds_lset_data_available_wrapper);
}

void on_publication_matched_wrapper(dds_entity_t writer, const dds_publication_matched_status_t status, void* arg) {

    JNIEnv* env = getJNIEnv();

    if (env == nullptr) {
        std::cout << "Failed to get JNIEnv*" << std::endl;
        abort();
    }

    jobject localListenerObj = env->NewLocalRef(g_WriterListenerObj);
    if (localListenerObj == nullptr) {
        std::cout << "Failed to create local reference for g_listenerObj" << std::endl;
        abort();
    }

    jclass listenerClass = env->GetObjectClass(localListenerObj);
    if (listenerClass == nullptr) {
        std::cout << "listenerClass is nullptr" << std::endl;
        abort();
    }

    jmethodID methodId = env->GetMethodID(listenerClass, "on_publication_matched", "(ILcom/seres/dds/sdk/core/PublicationMatchedStatus;)V");
    if (methodId == nullptr) {
        std::cout << "methodId is nullptr" << std::endl;
        abort();
    }

    jclass statusClass = g_PublicationMatchedStatus;
    if (statusClass == nullptr){
        std::cout << "Failed to get statusClass" << std::endl;
        abort();
    }

    jmethodID constructorId = env->GetMethodID(statusClass, "<init>", "(IIIIJ)V");
    if (constructorId == nullptr){
        std::cout << "Failed to get constructorId" << std::endl;
        jthrowable exc = env->ExceptionOccurred();
        if (exc) {
            env->ExceptionDescribe();
            env->ExceptionClear();
        }
        abort();
    }

    jobject statusObj = env->NewObject(statusClass, constructorId,
                                       static_cast<jint>(status.total_count),
                                       static_cast<jint>(status.total_count_change),
                                       static_cast<jint>(status.current_count),
                                       static_cast<jint>(status.current_count_change),
                                       static_cast<jlong>(status.last_subscription_handle));


    env->CallVoidMethod(localListenerObj, methodId, static_cast<jint>(writer), statusObj);
    env->DeleteLocalRef(localListenerObj);
}

extern "C" JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomLsetPublicationMatched(
        JNIEnv* env,
        jobject /* this */,
        jlong listenerid,
        jobject Listener,
        jint kind) {

    if (g_WriterListenerObj == nullptr) {
        g_WriterListenerObj = env->NewGlobalRef(Listener);
        if (g_WriterListenerObj == nullptr) {
            std::cout << "Failed to create global reference for Listener" << std::endl;
            abort();
        }
    }

    if (g_jvm == nullptr){
        if (env->GetJavaVM(&g_jvm) != 0) {
            std::cout << "Failed to get JavaVM" << std::endl;
            abort();
        }

        if (g_jvm == nullptr) {
            std::cout << "JavaVM is nullptr" << std::endl;
            abort();
        }
    }

    g_PublicationMatchedStatus = (jclass) env->NewGlobalRef(env->FindClass("com/seres/dds/sdk/core/PublicationMatchedStatus"));
    if (g_PublicationMatchedStatus == nullptr) {
        std::cout << "Failed to create global reference for SubscriptionMatchedStatus" << std::endl;
        abort();
    }

    dds_lset_publication_matched(reinterpret_cast<dds_listener_t*>(listenerid), on_publication_matched_wrapper);
}

extern "C"
JNIEXPORT jlong JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomCreateQos(JNIEnv *env, jobject thiz) {
    return kscom_create_qos();
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomDeleteQos(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    kscom_delete_qos(qos_ptr);
    kscom_delete_qos((dds_qos_t *)qos_ptr);
}


extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetReliability(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                          jint reliability_kind,
                                                          jlong blocking_time) {
    kscom_set_reliability((dds_qos_t*)qos_ptr, reliability_kind,blocking_time);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetDurability(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                         jint durabiliy_kind) {
    kscom_set_durability((dds_qos_t*)qos_ptr, durabiliy_kind);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetHistory(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                      jint history_kind, jint depth) {
    kscom_set_history((dds_qos_t*)qos_ptr, history_kind, depth);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetResourceLimits(JNIEnv *env, jobject thiz,
                                                             jlong qos_ptr, jint max_samples,
                                                             jint max_instances,
                                                             jint max_samples_per_instance) {
    kscom_set_resource_limits((dds_qos_t*)(qos_ptr), max_samples, max_instances, max_samples_per_instance);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetPresentation(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                           jint access_scope,
                                                           jboolean coherent_access,
                                                           jboolean ordered_access) {
    kscom_set_presentation((dds_qos_t*)qos_ptr, access_scope, coherent_access, ordered_access);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetLifespan(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                       jlong lifespan) {
    kscom_set_lifespan((dds_qos_t*)qos_ptr, lifespan);
}


extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetDeadline(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                       jlong deadline) {
    kscom_set_deadline((dds_qos_t*)qos_ptr, deadline);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetLatencyBudget(JNIEnv *env, jobject thiz,
                                                            jlong qos_ptr, jlong duration) {
    kscom_set_latencybudget((dds_qos_t*)qos_ptr, duration);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetOwnership(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                        jint kind) {
    kscom_set_ownership((dds_qos_t*)qos_ptr, kind);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetOwnershipStrength(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr, jint value) {
    kscom_set_ownership_strength((dds_qos_t*)qos_ptr, value);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetLiveliness(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                         jint kind, jlong lease_duration) {
    kscom_set_liveliness((dds_qos_t*)qos_ptr, kind, lease_duration);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetTimeBasedFilter(JNIEnv *env, jobject thiz,
                                                              jlong qos_ptr,
                                                              jlong minimum_separation) {
    kscom_set_time_based_filter((dds_qos_t*)qos_ptr, minimum_separation);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetTransportPriority(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr, jint value) {
    kscom_set_transport_priority((dds_qos_t*)qos_ptr,value);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetDestinationOrder(JNIEnv *env, jobject thiz,
                                                               jlong qos_ptr, jint kind) {
    kscom_set_destination_order((dds_qos_t*)qos_ptr, kind);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetWriterDataLifecycle(JNIEnv *env, jobject thiz,
                                                                  jlong qos_ptr,
                                                                  jboolean autodispose) {
    kscom_set_writer_data_lifecycle((dds_qos_t*)qos_ptr, autodispose);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetReaderDataLifecycle(JNIEnv *env, jobject thiz,
                                                                  jlong qos_ptr,
                                                                  jlong autopurge_nowriter_samples_delay,
                                                                  jlong autopurge_disposed_samples_delay) {
    kscom_set_reader_data_lifecycle((dds_qos_t*)qos_ptr,autopurge_nowriter_samples_delay,autopurge_disposed_samples_delay);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetDurabilityservice(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr,
                                                                jlong service_cleanup_delay,
                                                                jint history_kind,
                                                                jint history_depth,
                                                                jint max_samples,
                                                                jint max_instances,
                                                                jint max_samples_per_instance) {
    kscom_set_durability_service((dds_qos_t*)qos_ptr, service_cleanup_delay, history_kind, history_depth, max_samples, max_instances, max_samples_per_instance);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetUserdata(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                       jbyteArray value, jint sz) {
    jbyte* cvalue = env->GetByteArrayElements(value, nullptr);
    if(nullptr == cvalue){
        abort();
    }
    kscom_set_userdata((dds_qos_t *)qos_ptr, cvalue, sz);
    env->ReleaseByteArrayElements(value, cvalue, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetTopicdata(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                        jbyteArray value, jint sz) {
    jbyte* cvalue = env->GetByteArrayElements(value, nullptr);
    if(nullptr == cvalue){
        abort();
    }
    kscom_set_topicdata((dds_qos_t *)qos_ptr, cvalue, sz);
    env->ReleaseByteArrayElements(value, cvalue, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetGroupdata(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                        jbyteArray value, jint sz) {
    jbyte* cvalue = env->GetByteArrayElements(value, nullptr);
    if(nullptr == cvalue){
        abort();
    }
    kscom_set_groupdata((dds_qos_t *)qos_ptr, cvalue, sz);
    env->ReleaseByteArrayElements(value, cvalue, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetProperty(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                       jstring name, jstring value) {
    const char* cname = env->GetStringUTFChars(name,nullptr);
    if (nullptr == cname){
        abort();
    }
    const char* cvalue = env->GetStringUTFChars(value, nullptr);
    if(nullptr == cvalue){
        abort();
    }
    kscom_set_property((dds_qos_t *)qos_ptr, cname, cvalue);
    env->ReleaseStringUTFChars(value, cvalue);
    env->ReleaseStringUTFChars(name, cname);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetBinaryproperty(JNIEnv *env, jobject thiz,
                                                             jlong qos_ptr, jstring name,
                                                             jbyteArray value,jint sz) {
    // TODO: implement kScomSetBinaryproperty()
    const char* cname = env->GetStringUTFChars(name,nullptr);
    if (nullptr == cname){
        abort();
    }
    jbyte* cvalue = env->GetByteArrayElements(value, nullptr);
    if(nullptr == cvalue){
        abort();
    }

    kscom_set_bproperty((dds_qos_t*)qos_ptr, cname,cvalue, sz);
    env->ReleaseStringUTFChars(name,cname);
//    env->ReleaseStringUTFChars(name,inCStr);
    env->ReleaseByteArrayElements(value, cvalue, 0);

}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetIgnorelocal(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                          jint ignore) {
    kscom_set_ignorelocal((dds_qos_t*)qos_ptr, ignore);
}



extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetTypeconsistency(JNIEnv *env, jobject thiz,
                                                              jlong qos_ptr, jint kind,
                                                              jboolean ignore_sequence_bounds,
                                                              jboolean ignore_string_bounds,
                                                              jboolean ignore_member_names,
                                                              jboolean prevent_type_widening,
                                                              jboolean force_type_validation) {
    kscom_set_typeconsistency((dds_qos_t*)qos_ptr, kind, ignore_sequence_bounds, ignore_string_bounds, ignore_member_names, prevent_type_widening,force_type_validation);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetDataRepresentation(JNIEnv *env, jobject thiz,
                                                                 jlong qos_ptr, jint kind) {
    kscom_set_data_representation((dds_qos_t*)qos_ptr, kind);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomSetEntityName(JNIEnv *env, jobject thiz, jlong qos_ptr,
                                                         jstring name) {
    const char* cname = env->GetStringUTFChars(name,nullptr);
    if (nullptr == cname){
        abort();
    }
    kscom_set_entity_name((dds_qos_t*)qos_ptr, cname);
    env->ReleaseStringUTFChars(name,cname);
}


extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetReliability(JNIEnv *env, jobject thiz,
                                                          jlong qos_ptr) {
//    if(qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_RELIABILITY)){
//        return nullptr;
//    }

    dds_reliability_kind_t kind;
    long max_blocking_time;

    if(!dds_qget_reliability((dds_qos_t *) qos_ptr, &kind, &max_blocking_time)){
        return nullptr;
    }
    if(kind == DDS_RELIABILITY_BEST_EFFORT){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Reliability$BestEffort");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policy, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }

    jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Reliability$Reliable");
    if(policy == nullptr){
        return nullptr;
    }
    jmethodID  constructor = env->GetMethodID(policy, "<init>","(J)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policy);
        return nullptr;
    }
    jobject policyObject = env->NewObject(policy, constructor, max_blocking_time);
    if(policyObject == nullptr){
        env->DeleteLocalRef(policy);
        return nullptr;
    }
    env->DeleteLocalRef(policy);
    return policyObject;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetDurability(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_DURABILITY))
//        return nullptr;

    dds_durability_kind_t kind;

    if(!dds_qget_durability((dds_qos_t*)qos_ptr, &kind)){
        return nullptr;
    }
    if(DDS_DURABILITY_VOLATILE == kind){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Durability$Volatile");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID  constructor = env->GetMethodID(policy, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }
    if(DDS_DURABILITY_TRANSIENT_LOCAL == kind){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Durability$TransientLocal");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID  constructor = env->GetMethodID(policy, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }
    if(DDS_DURABILITY_TRANSIENT == kind){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Durability$Transient");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID  constructor = env->GetMethodID(policy, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }
    if(DDS_DURABILITY_PERSISTENT == kind){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$Durability$Persistent");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policy, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }
    return nullptr;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetHistory(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_HISTORY))
//        return nullptr;

    dds_history_kind_t kind;
    int32_t depth;

    if(!dds_qget_history(reinterpret_cast<const dds_qos_t *>(qos_ptr), &kind, &depth)){
        return nullptr;
    }
    if(kind == 1){
        jclass policyKeepAll = env->FindClass("com/seres/dds/sdk/Policy$History$KeepAll");
        if(policyKeepAll == nullptr){
            return nullptr;
        }
        jmethodID  constructor = env->GetMethodID(policyKeepAll, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyKeepAll);
            return nullptr;
        }
        jobject policyKeepAllObject = env->NewObject(policyKeepAll, constructor);
        if(policyKeepAllObject == nullptr){
            env->DeleteLocalRef(policyKeepAll);
            return nullptr;
        }
        env->DeleteLocalRef(policyKeepAll);
        return policyKeepAllObject;
    }

    jclass policyKeepLast = env->FindClass("com/seres/dds/sdk/Policy$History$KeepLast");
    if(policyKeepLast == nullptr){
        return nullptr;
    }
    jmethodID  constructor = env->GetMethodID(policyKeepLast, "<init>","(I)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyKeepLast);
        return nullptr;
    }
    jobject policyKeepLastObject = env->NewObject(policyKeepLast, constructor,depth);
    if(policyKeepLastObject == nullptr){
        env->DeleteLocalRef(policyKeepLast);
        return nullptr;
    }
    env->DeleteLocalRef(policyKeepLast);
    return policyKeepLastObject;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetResourceLimits(JNIEnv *env, jobject thiz,
                                                             jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_RESOURCE_LIMITS))
//        return nullptr;

    int32_t max_samples;
    int32_t max_instances;
    int32_t max_samples_per_instance;

    if(!dds_qget_resource_limits(reinterpret_cast<const dds_qos_t *>(qos_ptr), &max_samples, &max_instances, &max_samples_per_instance)){
        return nullptr;
    }
    jclass policyResourceLimits = env->FindClass("com/seres/dds/sdk/Policy$ResourceLimits");
    if(policyResourceLimits == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyResourceLimits, "<init>","(III)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyResourceLimits);
        return nullptr;
    }
    jobject policyResourceLimitsObject = env->NewObject(policyResourceLimits, constructor,max_samples,max_instances,max_samples_per_instance);
    if(policyResourceLimitsObject == nullptr){
        env->DeleteLocalRef(policyResourceLimits);
        return nullptr;
    }
    env->DeleteLocalRef(policyResourceLimits);
    return policyResourceLimitsObject;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetPresentation(JNIEnv *env, jobject thiz,
                                                           jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_PRESENTATION))
//        return nullptr;

    dds_presentation_access_scope_kind_t access_scope;
    bool coherent_access;
    bool ordered_access;

    if(!dds_qget_presentation(reinterpret_cast<const dds_qos_t *>(qos_ptr), &access_scope, &coherent_access, &ordered_access)){
        return nullptr;
    }
    if(access_scope == DDS_PRESENTATION_INSTANCE){
        jclass policyPresentation = env->FindClass("com/seres/dds/sdk/Policy$PresentationAccessScope$Instance");
        if(policyPresentation == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyPresentation, "<init>","(ZZ)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        jobject policyPresentationObject = env->NewObject(policyPresentation, constructor,coherent_access,ordered_access);
        if(policyPresentationObject == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        env->DeleteLocalRef(policyPresentation);
        return policyPresentationObject;

    }else if(access_scope == DDS_PRESENTATION_TOPIC){
        jclass policyPresentation = env->FindClass("com/seres/dds/sdk/Policy$PresentationAccessScope$Topic");
        if(policyPresentation == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyPresentation, "<init>","(ZZ)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        jobject policyPresentationObject = env->NewObject(policyPresentation, constructor,coherent_access,ordered_access);
        if(policyPresentationObject == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        env->DeleteLocalRef(policyPresentation);
        return policyPresentationObject;
    }else if(access_scope == DDS_PRESENTATION_GROUP){
        jclass policyPresentation = env->FindClass("com/seres/dds/sdk/Policy$PresentationAccessScope$Group");
        if(policyPresentation == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyPresentation, "<init>","(ZZ)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        jobject policyPresentationObject = env->NewObject(policyPresentation, constructor,coherent_access,ordered_access);
        if(policyPresentationObject == nullptr){
            env->DeleteLocalRef(policyPresentation);
            return nullptr;
        }
        env->DeleteLocalRef(policyPresentation);
        return policyPresentationObject;
    }

    return nullptr;
}
extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetLifespan(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_LIFESPAN))
//        return nullptr;

    long lifespan;

    if(!dds_qget_lifespan(reinterpret_cast<const dds_qos_t *>(qos_ptr), &lifespan)){
        return nullptr;
    }

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Lifespan");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,lifespan);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}
extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetDeadline(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_DEADLINE))
//        return nullptr;

    long deadline;

    if(!dds_qget_deadline(reinterpret_cast<const dds_qos_t *>(qos_ptr), &deadline)){
        return nullptr;
    }

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Deadline");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,deadline);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetLatencyBudget(JNIEnv *env, jobject thiz,
                                                            jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_LATENCY_BUDGET))
//        return nullptr;

    long latencybudget;

    if(!dds_qget_latency_budget(reinterpret_cast<const dds_qos_t *>(qos_ptr), &latencybudget)){
        return nullptr;
    }

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$LatencyBudget");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,latencybudget);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetOwnership(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_OWNERSHIP))
//        return nullptr;

    dds_ownership_kind_t kind;

    if(!dds_qget_ownership(reinterpret_cast<const dds_qos_t *>(qos_ptr), &kind)){
        return nullptr;
    }
    if(kind == DDS_OWNERSHIP_SHARED){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Ownership$Shared");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }else if(kind == DDS_OWNERSHIP_EXCLUSIVE){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Ownership$Exclusive");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }
    return nullptr;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetOwnershipStrength(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_OWNERSHIP_STRENGTH))
//        return nullptr;

    int32_t value;
    if(!dds_qget_ownership_strength(reinterpret_cast<const dds_qos_t *>(qos_ptr), &value)){
        return nullptr;
    }
    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$OwnershipStrength");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(I)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,value);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}


extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetLiveliness(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_LIVELINESS))
//        return nullptr;

    dds_liveliness_kind_t kind;
    long lease_duration;

    if(!dds_qget_liveliness(reinterpret_cast<const dds_qos_t *>(qos_ptr), &kind, &lease_duration)){
        return nullptr;
    }

    if(kind == DDS_LIVELINESS_AUTOMATIC){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Liveliness$Automatic");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor,lease_duration);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }else if(kind == DDS_LIVELINESS_MANUAL_BY_PARTICIPANT){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Liveliness$ManualByParticipant");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor,lease_duration);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }else if(kind == DDS_LIVELINESS_MANUAL_BY_TOPIC){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Liveliness$ManualByTopic");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor,lease_duration);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }

    return nullptr;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetTimeBasedFilter(JNIEnv *env, jobject thiz,
                                                              jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_TIME_BASED_FILTER))
//        return nullptr;

    long minimum_separation;

    if(!(dds_qget_time_based_filter(reinterpret_cast<const dds_qos_t *>(qos_ptr), &minimum_separation))){
        return nullptr;
    }
    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$TimeBasedFilter");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(J)V");
    if(constructor == nullptr){
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,minimum_separation);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetPartition(JNIEnv *env, jobject thiz, jlong qos_ptr) {
    // TODO: implement kScomGetPartition()
    return nullptr;
}


extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetTransportPriority(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_TRANSPORT_PRIORITY))
//        return nullptr;

    int32_t value;
    if(!(dds_qget_transport_priority(reinterpret_cast<const dds_qos_t *>(qos_ptr), &value))){
        return nullptr;
    }

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$TransportPriority");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(I)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,value);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}


extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetDestinationOrder(JNIEnv *env, jobject thiz,
                                                               jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_DESTINATION_ORDER))
//        return nullptr;

    dds_destination_order_kind_t kind;
    if(!dds_qget_destination_order(reinterpret_cast<const dds_qos_t *>(qos_ptr), &kind)){
        return nullptr;
    }
    if(kind == DDS_DESTINATIONORDER_BY_RECEPTION_TIMESTAMP){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$DestinationOrder$ByReceptionTimestamp");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        return policyobj;
    }else if(kind == DDS_DESTINATIONORDER_BY_SOURCE_TIMESTAMP){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$DestinationOrder$BySourceTimestamp");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        return policyobj;
    }
    return nullptr;
}
extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetWriteDataLifecycle(JNIEnv *env, jobject thiz,
                                                                 jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_ADLINK_WRITER_DATA_LIFECYCLE))
//        return nullptr;

    bool autodispose;
    if(!dds_qget_writer_data_lifecycle(reinterpret_cast<const dds_qos_t *>(qos_ptr), &autodispose)){
        return nullptr;
    }
    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$WriteDataLifecycle");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(Z)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,autodispose);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetReaderDataLifecycle(JNIEnv *env, jobject thiz,
                                                                  jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_ADLINK_WRITER_DATA_LIFECYCLE))
//        return nullptr;

    long autopurge_nowriter_samples_delay;
    long autopurge_disposed_samples_delay;

    if(!dds_qget_reader_data_lifecycle(reinterpret_cast<const dds_qos_t *>(qos_ptr), &autopurge_nowriter_samples_delay, &autopurge_disposed_samples_delay)){
        return nullptr;
    }
    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$ReaderDataLifecycle");
    if(policyclz == nullptr){
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","(JJ)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor,autopurge_nowriter_samples_delay,autopurge_disposed_samples_delay);
    if(policyobj == nullptr){
        env->DeleteLocalRef(policyclz);
        return nullptr;
    }
    env->DeleteLocalRef(policyclz);
    return policyobj;
}
extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetDurabilityService(JNIEnv *env, jobject thiz,
                                                                jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_DURABILITY_SERVICE))
//        return nullptr;

    long service_cleanup_delay;
    dds_history_kind_t kind;
    int32_t history_depth;
    int32_t max_samples;
    int32_t max_instances;
    int32_t max_samples_per_instance;

    if(!dds_qget_durability_service(reinterpret_cast<const dds_qos_t *>(qos_ptr),&service_cleanup_delay, &kind, &history_depth, &max_samples, &max_instances, &max_samples_per_instance)){
        return nullptr;
    }
    if(kind == DDS_HISTORY_KEEP_LAST){
        jclass policyhistoryclz = env->FindClass("com/seres/dds/sdk/Policy$History$KeepLast");
        if(policyhistoryclz == nullptr){
            return nullptr;
        }
        jmethodID constructorhistory = env->GetMethodID(policyhistoryclz, "<init>","(I)V");
        if(constructorhistory == nullptr){
            env->DeleteLocalRef(policyhistoryclz);
            return nullptr;
        }
        jobject policyhistoryobj = env->NewObject(policyhistoryclz, constructorhistory, history_depth);
        if(policyhistoryobj == nullptr){
            env->DeleteLocalRef(policyhistoryclz);
            return nullptr;
        }

        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$DurabilityService");
        if(policyclz == nullptr){
            env->DeleteLocalRef(policyhistoryclz);
            env->DeleteLocalRef(policyhistoryobj);
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","(JLcom/seres/dds/sdk/Policy$History;III)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            env->DeleteLocalRef(policyhistoryclz);
            env->DeleteLocalRef(policyhistoryobj);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor,service_cleanup_delay,policyhistoryobj,max_samples,max_instances,max_samples_per_instance);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyhistoryobj);
            env->DeleteLocalRef(policyhistoryclz);
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyhistoryobj);
        env->DeleteLocalRef(policyhistoryclz);
        env->DeleteLocalRef(policyclz);
        return policyobj;

    }else if(kind == DDS_HISTORY_KEEP_ALL){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$History$KeepAll");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }

    return nullptr;
}
extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetIgnoreLocal(JNIEnv *env, jobject thiz,
                                                          jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_CYCLONE_IGNORELOCAL))
//        return nullptr;

    dds_ignorelocal_kind_t ignore;

    if(!dds_qget_ignorelocal(reinterpret_cast<const dds_qos_t *>(qos_ptr), &ignore)){
        return nullptr;
    }

    if(ignore == DDS_IGNORELOCAL_NONE){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$IgnoreLocal$Nothing");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }else if(ignore == DDS_IGNORELOCAL_PARTICIPANT){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$IgnoreLocal$Participant");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        env->DeleteLocalRef(policyclz);
        return policyobj;
    }else if(ignore == DDS_IGNORELOCAL_PROCESS){
        jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$IgnoreLocal$Process");
        if(policyclz == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policyclz, "<init>","()V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }
        jobject policyobj = env->NewObject(policyclz, constructor);
        if(policyobj == nullptr){
            env->DeleteLocalRef(policyclz);
            return nullptr;
        }

        env->DeleteLocalRef(policyclz);
        return policyobj;
    }
    return nullptr;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetUserdata(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_USER_DATA))
//        return nullptr;

    unsigned char* value;
    size_t  sz;

    if(!dds_qget_userdata(reinterpret_cast<const dds_qos_t *>(qos_ptr),
                          reinterpret_cast<void **>(&value), &sz)){
        return nullptr;
    }

    if(value == nullptr){
        return nullptr;
    }
    jbyteArray byteArray = env->NewByteArray(sz);
    if(byteArray == nullptr){
        dds_free(value);
        return nullptr;
    }
    env->SetByteArrayRegion(byteArray, 0, sz, reinterpret_cast<const jbyte*>(value));

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Userdata");
    if(policyclz == nullptr){
        env->DeleteLocalRef(byteArray);
        dds_free(value);
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","([B)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor, byteArray);
    if(policyobj == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    env->DeleteLocalRef(byteArray);
    env->DeleteLocalRef(policyclz);
    dds_free(value);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetTopicdata(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_TOPIC_DATA))
//        return nullptr;

    unsigned char* value;
    size_t  sz;

    if(!dds_qget_topicdata(reinterpret_cast<const dds_qos_t *>(qos_ptr),
                           reinterpret_cast<void **>(&value), &sz)){
        return nullptr;
    }

    if(value == nullptr){
        return nullptr;
    }
    jbyteArray byteArray = env->NewByteArray(sz);
    if(byteArray == nullptr){
        dds_free(value);
        return nullptr;
    }
    env->SetByteArrayRegion(byteArray, 0, sz, reinterpret_cast<const jbyte*>(value));

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Topicdata");
    if(policyclz == nullptr){
        env->DeleteLocalRef(byteArray);
        dds_free(value);
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","([B)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor, byteArray);
    if(policyobj == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    env->DeleteLocalRef(byteArray);
    env->DeleteLocalRef(policyclz);
    dds_free(value);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetGroupdata(JNIEnv *env, jobject thiz, jlong qos_ptr) {
//    if (qos_ptr == 0 || !(((dds_qos_t*)qos_ptr)->present & DDSI_QP_GROUP_DATA))
//        return nullptr;

    unsigned char* value;
    size_t  sz;

    if(!dds_qget_groupdata(reinterpret_cast<const dds_qos_t *>(qos_ptr),
                           reinterpret_cast<void **>(&value), &sz)){
        return nullptr;
    }

    if(value == nullptr){
        return nullptr;
    }
    jbyteArray byteArray = env->NewByteArray(sz);
    if(byteArray == nullptr){
        dds_free(value);
        return nullptr;
    }
    env->SetByteArrayRegion(byteArray, 0, sz, reinterpret_cast<const jbyte*>(value));

    jclass policyclz = env->FindClass("com/seres/dds/sdk/Policy$Groupdata");
    if(policyclz == nullptr){
        env->DeleteLocalRef(byteArray);
        dds_free(value);
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policyclz, "<init>","([B)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    jobject policyobj = env->NewObject(policyclz, constructor, byteArray);
    if(policyobj == nullptr){
        env->DeleteLocalRef(byteArray);
        env->DeleteLocalRef(policyclz);
        dds_free(value);
        return nullptr;
    }
    env->DeleteLocalRef(byteArray);
    env->DeleteLocalRef(policyclz);
    dds_free(value);
    return policyobj;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetProperty(JNIEnv *env, jobject thiz, jlong qos_ptr) {
    uint32_t num;
    char** names;
    if(!dds_qget_propnames(reinterpret_cast<const dds_qos_t *>(qos_ptr), &num, &names)){
        return nullptr;
    }
    if(names == nullptr){
        return nullptr;
    }
    jclass propertyClass = env->FindClass("com/seres/dds/sdk/Policy$Property");
    if (propertyClass == nullptr) {
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr; // 处理类未找到的情况
    }

    // 获取 Property 类的构造函数
    jmethodID constructor = env->GetMethodID(propertyClass, "<init>", "(Ljava/lang/String;Ljava/lang/String;)V");
    if (constructor == nullptr) {
        env->DeleteLocalRef(propertyClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr; // 处理构造函数未找到的情况
    }


    jclass arrayListClass = env->FindClass("java/util/ArrayList");
    if(arrayListClass == nullptr){
        env->DeleteLocalRef(propertyClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr; // 处理构造函数未找到的情况
    }

    jmethodID arrayListConstructor = env->GetMethodID(arrayListClass, "<init>", "()V");
    jobject arrayListObject = env->NewObject(arrayListClass, arrayListConstructor);
    if(arrayListObject == nullptr){
        env->DeleteLocalRef(propertyClass);
        env->DeleteLocalRef(arrayListClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }
    jmethodID addMethod = env->GetMethodID(arrayListClass, "add", "(Ljava/lang/Object;)Z");
    if(addMethod == nullptr){
        env->DeleteLocalRef(propertyClass);
        env->DeleteLocalRef(arrayListClass);
        env->DeleteLocalRef(arrayListObject);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }
    for(int i = 0; i < num; ++i){
        char* value;
        if(!dds_qget_prop(reinterpret_cast<const dds_qos_t *>(qos_ptr), names[i], &value)){
            throw std::runtime_error("Internal QOS property structure is corrupt!"); // exception
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(propertyClass);
            env->DeleteLocalRef(arrayListClass);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }
        jstring jkey = env->NewStringUTF(names[i]);
        if(jkey == nullptr){
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(propertyClass);
            env->DeleteLocalRef(arrayListClass);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }
        jstring jvalue = env->NewStringUTF(value);
        if(jvalue == nullptr){
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(propertyClass);
            env->DeleteLocalRef(arrayListClass);
            env->DeleteLocalRef(jkey);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }
        jobject propertyObject = env->NewObject(propertyClass, constructor, jkey,jvalue);
        if(propertyObject == nullptr){
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(propertyClass);
            env->DeleteLocalRef(arrayListClass);
            env->DeleteLocalRef(jkey);
            env->DeleteLocalRef(jvalue);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
        }

        env->CallBooleanMethod(arrayListObject, addMethod, propertyObject);
        env->DeleteLocalRef(propertyObject);
        env->DeleteLocalRef(jvalue);
        env->DeleteLocalRef(jkey);
        dds_free(value);
    }
    for(int i = 0; i < num; ++i){
        dds_free(names[i]);
    }
    dds_free(names);

    env->DeleteLocalRef(propertyClass);
    env->DeleteLocalRef(arrayListClass);

    return arrayListObject;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetBinaryProperty(JNIEnv *env, jobject thiz,
                                                             jlong qos_ptr) {
    uint32_t num;
    char** names;
    if(!dds_qget_bpropnames(reinterpret_cast<const dds_qos_t *>(qos_ptr), &num, &names)){
        return nullptr;
    }
    if(names == nullptr){
        return nullptr;
    }

    jclass bpropertyClass = env->FindClass("com/seres/dds/sdk/Policy$BinaryProperty");
    if (bpropertyClass == nullptr) {
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr; // 处理类未找到的情况
    }

    // 获取 Property 类的构造函数
    jmethodID constructor = env->GetMethodID(bpropertyClass, "<init>", "(Ljava/lang/String;[B)V");
    if (constructor == nullptr) {
        env->DeleteLocalRef(bpropertyClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr; // 处理构造函数未找到的情况
    }


    jclass arrayListClass = env->FindClass("java/util/ArrayList");
    if(arrayListClass == nullptr){
        env->DeleteLocalRef(bpropertyClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }
    jmethodID arrayListConstructor = env->GetMethodID(arrayListClass, "<init>", "()V");
    if(arrayListConstructor == nullptr){
        env->DeleteLocalRef(bpropertyClass);
        env->DeleteLocalRef(arrayListClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }

    jobject arrayListObject = env->NewObject(arrayListClass, arrayListConstructor);
    if(arrayListObject == nullptr){
        env->DeleteLocalRef(bpropertyClass);
        env->DeleteLocalRef(arrayListClass);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }

    jmethodID addMethod = env->GetMethodID(arrayListClass, "add", "(Ljava/lang/Object;)Z");
    if(addMethod == nullptr){
        env->DeleteLocalRef(bpropertyClass);
        env->DeleteLocalRef(arrayListClass);
        env->DeleteLocalRef(arrayListObject);
        for(int i = 0; i < num; ++i){
            dds_free(names[i]);
        }
        dds_free(names);
        return nullptr;
    }

    for(int i = 0; i < num; ++i){
        size_t sz;
        char* value;
        if(!dds_qget_bprop(reinterpret_cast<const dds_qos_t *>(qos_ptr), names[i],
                           reinterpret_cast<void **>(&value), &sz)){

            throw std::runtime_error("Internal QOS property structure is corrupt!"); // exception
            dds_free(value);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }

        jstring jkey = env->NewStringUTF(names[i]);
        if(jkey == nullptr){
            env->DeleteLocalRef(bpropertyClass);
            env->DeleteLocalRef(arrayListClass);
            env->DeleteLocalRef(arrayListObject);
            dds_free(value);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }
        jbyteArray jvalue = env->NewByteArray(sz);
        if(jvalue == nullptr){
            env->DeleteLocalRef(bpropertyClass);
            env->DeleteLocalRef(arrayListClass);
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(jkey);
            dds_free(value);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }
        env->SetByteArrayRegion(jvalue, 0, sz, reinterpret_cast<const jbyte*>(value));
        jobject propertyObject = env->NewObject(bpropertyClass, constructor, jkey, jvalue);
        if(propertyObject == nullptr){
            env->DeleteLocalRef(bpropertyClass);
            env->DeleteLocalRef(arrayListClass);
            env->DeleteLocalRef(arrayListObject);
            env->DeleteLocalRef(jkey);
            env->DeleteLocalRef(jvalue);
            dds_free(value);
            for(int i = 0; i < num; ++i){
                dds_free(names[i]);
            }
            dds_free(names);
            return nullptr;
        }


        env->CallBooleanMethod(arrayListObject, addMethod, propertyObject);

        env->DeleteLocalRef(propertyObject);
        env->DeleteLocalRef(jkey);
        env->DeleteLocalRef(jvalue);
        dds_free(value);
    }

    env->DeleteLocalRef(bpropertyClass);
    env->DeleteLocalRef(arrayListClass);

    for(int i = 0; i < num; ++i){
        dds_free(names[i]);
    }
    dds_free(names);

    return arrayListObject;
}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetTypeConsistency(JNIEnv *env, jobject thiz,
                                                              jlong qos_ptr) {
    dds_type_consistency_kind_t kind;
    bool ignore_sequence_bounds;
    bool ignore_string_bounds;
    bool ignore_member_names;
    bool prevent_type_widening;
    bool force_type_validation;
    if(!dds_qget_type_consistency(reinterpret_cast<const dds_qos_t *>(qos_ptr), &kind, &ignore_sequence_bounds, &ignore_string_bounds, &ignore_member_names, &prevent_type_widening, &force_type_validation)){
        return nullptr;
    }
//    DDS_TYPE_CONSISTENCY_DISALLOW_TYPE_COERCION, /**< Do not allow type coercion */
//            DDS_TYPE_CONSISTENCY_ALLOW_TYPE_COERCION /**< Allow type coercion */
    if(kind == DDS_TYPE_CONSISTENCY_DISALLOW_TYPE_COERCION){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$TypeConsistency$DisallowTypeCoercion");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID constructor = env->GetMethodID(policy, "<init>","(Z)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor,force_type_validation);
        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }else if(kind == DDS_TYPE_CONSISTENCY_ALLOW_TYPE_COERCION){
        jclass policy = env->FindClass("com/seres/dds/sdk/Policy$TypeConsistency$AllowTypeCoercion");
        if(policy == nullptr){
            return nullptr;
        }
        jmethodID  constructor = env->GetMethodID(policy, "<init>","(ZZZZZ)V");
        if(constructor == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        jobject policyObject = env->NewObject(policy, constructor,ignore_sequence_bounds, ignore_string_bounds, ignore_member_names, prevent_type_widening, force_type_validation);

        if(policyObject == nullptr){
            env->DeleteLocalRef(policy);
            return nullptr;
        }
        env->DeleteLocalRef(policy);
        return policyObject;
    }
    return nullptr;

}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetDataRepresentation(JNIEnv *env, jobject thiz,
                                                                 jlong qos_ptr) {
    uint32_t n = 0;
    dds_data_representation_id_t *values;
    if(!(dds_qget_data_representation(reinterpret_cast<const dds_qos_t *>(qos_ptr), &n, &values))){
        return nullptr;
    }
    if(values){
        return nullptr;
    }
    bool use_cdrv0 = false;
    bool use_xcdrv2 = false;
    for(int i = 0; i < n; ++i){
        if(values[i] == 0){
            use_cdrv0 = true;
        }else if(values[i] == 2){
            use_xcdrv2 = true;
        }
    }

    dds_free(values);

    jclass policy = env->FindClass("com/seres/dds/sdk/Policy$DataRepresentation");
    if(policy == nullptr){
        return nullptr;
    }
    jmethodID  constructor = env->GetMethodID(policy, "<init>","(ZZ)V");
    if(constructor == nullptr){
        env->DeleteLocalRef(policy);
        return nullptr;
    }
    jobject policyObject = env->NewObject(policy, constructor,use_cdrv0,use_xcdrv2);

    if(policyObject == nullptr){
        env->DeleteLocalRef(policy);
        return nullptr;
    }
    env->DeleteLocalRef(policy);
    return policyObject;

}

extern "C"
JNIEXPORT jobject JNICALL
Java_com_seres_dds_sdk_KScomNativeLib_kScomGetEntityName(JNIEnv *env, jobject thiz, jlong qos_ptr) {
    char* name;
    if(!dds_qget_entity_name(reinterpret_cast<const dds_qos_t *>(qos_ptr), &name)){
        return nullptr;
    }
    if(name == nullptr){
        return nullptr;
    }
    jstring jname = env->NewStringUTF(name);
    if(jname == nullptr){
        dds_free(name);
        return nullptr;
    }

    jclass policy = env->FindClass("com/seres/dds/sdk/Policy$EntityName");
    if(policy == nullptr){
        dds_free(name);
        env->DeleteLocalRef(jname);
        return nullptr;
    }
    jmethodID constructor = env->GetMethodID(policy, "<init>","(Ljava/lang/String;)V");
    if(constructor == nullptr){
        dds_free(name);
        env->DeleteLocalRef(policy);
        env->DeleteLocalRef(jname);
        return nullptr;
    }
    jobject policyObject = env->NewObject(policy, constructor,jname);

    if(policyObject == nullptr){
        env->DeleteLocalRef(policy);
        env->DeleteLocalRef(jname);
        dds_free(name);
        return nullptr;
    }
    env->DeleteLocalRef(policy);
    env->DeleteLocalRef(jname);
    dds_free(name);
    return policyObject;
}
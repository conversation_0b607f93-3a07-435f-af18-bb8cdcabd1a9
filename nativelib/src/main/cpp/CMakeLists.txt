# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html.
# For more examples on how to use CMake, see https://github.com/android/ndk-samples.

# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.22.1)

project("nativelib")

add_library(kscom SHARED kscom.c kscom_bridge.cpp)

message(${CMAKE_SOURCE_DIR})

target_include_directories(kscom PUBLIC ${CMAKE_SOURCE_DIR}/include)

#find_library(log-lib log)

# Link the correct libtest.so depending on the ABI
add_library(ddsc-lib SHARED IMPORTED)
# find_package(SomeLibrary REQUIRED)

if(NOT DEFINED ANDROID_ABI)
# 如果没有设置，则将其默认值设置为 "x86" linux 所需使用的库在x86_86目录下面
    if(NOT $ENV{JAVA_HOME} STREQUAL "")
        set(JAVA_HOME $ENV{JAVA_HOME})
        message(STATUS "Using JAVA_HOME: ${JAVA_HOME}")
    else()
        message(WARNING "JAVA_HOME is not set. Please set JAVA_HOME environment variable.")
    endif()
    find_package(JNI)
    if(JNI_FOUND)
        include_directories(${JNI_INCLUDE_DIRS})
    else()
        message(FATAL_ERROR "JNI not found")
    endif()
    set(ANDROID_ABI "x86")
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})
    target_compile_definitions(kscom PRIVATE LinuxVersion) #LinuxVersion 宏开关
endif()


set_target_properties(ddsc-lib PROPERTIES IMPORTED_LOCATION ${CMAKE_SOURCE_DIR}/libs/${ANDROID_ABI}/libddsc.so)

install(TARGETS kscom
        LIBRARY DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}  # 安装到 lib 目录
        ARCHIVE DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI}
        RUNTIME DESTINATION ${CMAKE_CURRENT_SOURCE_DIR}/../jniLibs/${ANDROID_ABI})


#target_link_libraries(kscom PUBLIC ddsc-lib ${log-lib})
target_link_libraries(kscom PUBLIC ddsc-lib log)